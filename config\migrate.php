<?php
// Database Migration Script
// Run this file to update your database with missing columns

require_once 'database.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Database Migration - Sanskar Institute</title>
    <script src='https://cdn.tailwindcss.com'></script>
</head>
<body class='bg-gray-100'>
    <div class='min-h-screen flex items-center justify-center py-12 px-4'>
        <div class='max-w-2xl w-full bg-white rounded-lg shadow-xl p-8'>
            <h1 class='text-2xl font-bold text-gray-900 mb-6'>Database Migration</h1>";

try {
    echo "<div class='space-y-4'>";
    
    // Check if description column exists in departments table
    echo "<div class='flex items-center'>";
    $stmt = $db->query("SHOW COLUMNS FROM departments LIKE 'description'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='text-yellow-600 mr-2'>⚠️</div>";
        echo "<span>Adding description column to departments table...</span>";
        $db->exec("ALTER TABLE departments ADD COLUMN description TEXT AFTER code");
        echo "<div class='text-green-600 ml-2'>✅ Done</div>";
    } else {
        echo "<div class='text-green-600 mr-2'>✅</div>";
        echo "<span>Description column already exists in departments table</span>";
    }
    echo "</div>";
    
    // Check if description column exists in courses table
    echo "<div class='flex items-center'>";
    $stmt = $db->query("SHOW COLUMNS FROM courses LIKE 'description'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='text-yellow-600 mr-2'>⚠️</div>";
        echo "<span>Adding description column to courses table...</span>";
        $db->exec("ALTER TABLE courses ADD COLUMN description TEXT AFTER credits");
        echo "<div class='text-green-600 ml-2'>✅ Done</div>";
    } else {
        echo "<div class='text-green-600 mr-2'>✅</div>";
        echo "<span>Description column already exists in courses table</span>";
    }
    echo "</div>";
    
    // Check if semester column exists in classes table
    echo "<div class='flex items-center'>";
    $stmt = $db->query("SHOW COLUMNS FROM classes LIKE 'semester'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='text-yellow-600 mr-2'>⚠️</div>";
        echo "<span>Adding semester column to classes table...</span>";
        $db->exec("ALTER TABLE classes ADD COLUMN semester ENUM('Fall', 'Spring', 'Summer') AFTER section");
        echo "<div class='text-green-600 ml-2'>✅ Done</div>";
    } else {
        echo "<div class='text-green-600 mr-2'>✅</div>";
        echo "<span>Semester column already exists in classes table</span>";
    }
    echo "</div>";
    
    // Check if schedule column exists in classes table
    echo "<div class='flex items-center'>";
    $stmt = $db->query("SHOW COLUMNS FROM classes LIKE 'schedule'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='text-yellow-600 mr-2'>⚠️</div>";
        echo "<span>Adding schedule column to classes table...</span>";
        $db->exec("ALTER TABLE classes ADD COLUMN schedule VARCHAR(100) AFTER academic_year");
        echo "<div class='text-green-600 ml-2'>✅ Done</div>";
    } else {
        echo "<div class='text-green-600 mr-2'>✅</div>";
        echo "<span>Schedule column already exists in classes table</span>";
    }
    echo "</div>";
    
    // Check if room column exists in classes table
    echo "<div class='flex items-center'>";
    $stmt = $db->query("SHOW COLUMNS FROM classes LIKE 'room'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='text-yellow-600 mr-2'>⚠️</div>";
        echo "<span>Adding room column to classes table...</span>";
        $db->exec("ALTER TABLE classes ADD COLUMN room VARCHAR(50) AFTER schedule");
        echo "<div class='text-green-600 ml-2'>✅ Done</div>";
    } else {
        echo "<div class='text-green-600 mr-2'>✅</div>";
        echo "<span>Room column already exists in classes table</span>";
    }
    echo "</div>";
    
    // Check if settings table exists
    echo "<div class='flex items-center'>";
    $stmt = $db->query("SHOW TABLES LIKE 'settings'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='text-yellow-600 mr-2'>⚠️</div>";
        echo "<span>Creating settings table...</span>";
        $db->exec("
            CREATE TABLE settings (
                id INT PRIMARY KEY AUTO_INCREMENT,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        echo "<div class='text-green-600 ml-2'>✅ Done</div>";
    } else {
        echo "<div class='text-green-600 mr-2'>✅</div>";
        echo "<span>Settings table already exists</span>";
    }
    echo "</div>";
    
    // Insert default settings
    echo "<div class='flex items-center'>";
    echo "<div class='text-yellow-600 mr-2'>⚠️</div>";
    echo "<span>Inserting default settings...</span>";
    
    $default_settings = [
        ['institute_name', 'Sanskar Institute of Management & Information Technology', 'Name of the institute'],
        ['academic_year', '2024-2025', 'Current academic year'],
        ['attendance_threshold', '75', 'Minimum attendance percentage required'],
        ['late_threshold', '15', 'Minutes after start time to mark as late'],
        ['timezone', 'Asia/Kolkata', 'System timezone'],
        ['email_notifications', '1', 'Enable email notifications'],
        ['sms_notifications', '0', 'Enable SMS notifications'],
        ['auto_backup', '1', 'Enable automatic backups'],
        ['backup_frequency', 'daily', 'Backup frequency'],
        ['session_timeout', '30', 'Session timeout in minutes'],
        ['max_login_attempts', '5', 'Maximum login attempts'],
        ['password_min_length', '8', 'Minimum password length'],
        ['require_password_change', '90', 'Password change required in days']
    ];
    
    $stmt = $db->prepare("
        INSERT IGNORE INTO settings (setting_key, setting_value, description) 
        VALUES (?, ?, ?)
    ");
    
    foreach ($default_settings as $setting) {
        $stmt->execute($setting);
    }
    echo "<div class='text-green-600 ml-2'>✅ Done</div>";
    echo "</div>";
    
    // Update existing classes to have default semester if NULL
    echo "<div class='flex items-center'>";
    echo "<div class='text-yellow-600 mr-2'>⚠️</div>";
    echo "<span>Updating existing classes with default semester...</span>";
    $db->exec("UPDATE classes SET semester = 'Fall' WHERE semester IS NULL OR semester = ''");
    echo "<div class='text-green-600 ml-2'>✅ Done</div>";
    echo "</div>";
    
    echo "</div>";
    
    echo "<div class='mt-8 p-4 bg-green-50 border border-green-200 rounded-md'>
            <div class='flex'>
                <div class='text-green-600 mr-2'>✅</div>
                <div>
                    <h3 class='text-lg font-semibold text-green-900'>Migration Completed Successfully!</h3>
                    <p class='text-green-700 mt-1'>All database updates have been applied. You can now use all features of the attendance system.</p>
                </div>
            </div>
          </div>";
    
    echo "<div class='mt-6 text-center'>
            <a href='../admin/dashboard.php' class='inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700'>
                Go to Admin Dashboard
            </a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='mt-8 p-4 bg-red-50 border border-red-200 rounded-md'>
            <div class='flex'>
                <div class='text-red-600 mr-2'>❌</div>
                <div>
                    <h3 class='text-lg font-semibold text-red-900'>Migration Failed</h3>
                    <p class='text-red-700 mt-1'>Error: " . htmlspecialchars($e->getMessage()) . "</p>
                </div>
            </div>
          </div>";
    
    echo "<div class='mt-6 text-center'>
            <button onclick='location.reload()' class='inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700'>
                Retry Migration
            </button>
          </div>";
}

echo "        </div>
    </div>
</body>
</html>";
?>
