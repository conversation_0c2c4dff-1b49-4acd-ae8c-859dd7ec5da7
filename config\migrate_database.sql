-- Database Migration Script for Attendance System
-- Run this script to update your existing database with missing columns

USE attendance_system;

-- Add description column to departments table if it doesn't exist
ALTER TABLE departments 
ADD COLUMN IF NOT EXISTS description TEXT AFTER code;

-- Add description column to courses table if it doesn't exist
ALTER TABLE courses 
ADD COLUMN IF NOT EXISTS description TEXT AFTER credits;

-- Add semester column to classes table if it doesn't exist
ALTER TABLE classes 
ADD COLUMN IF NOT EXISTS semester ENUM('Fall', 'Spring', 'Summer') AFTER section;

-- Add schedule column to classes table if it doesn't exist
ALTER TABLE classes 
ADD COLUMN IF NOT EXISTS schedule VARCHAR(100) AFTER academic_year;

-- Add room column to classes table if it doesn't exist
ALTER TABLE classes 
ADD COLUMN IF NOT EXISTS room VARCHAR(50) AFTER schedule;

-- Create settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default settings if they don't exist
INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES
('institute_name', 'Sanskar Institute of Management & Information Technology', 'Name of the institute'),
('academic_year', '2024-2025', 'Current academic year'),
('attendance_threshold', '75', 'Minimum attendance percentage required'),
('late_threshold', '15', 'Minutes after start time to mark as late'),
('timezone', 'Asia/Kolkata', 'System timezone'),
('email_notifications', '1', 'Enable email notifications'),
('sms_notifications', '0', 'Enable SMS notifications'),
('auto_backup', '1', 'Enable automatic backups'),
('backup_frequency', 'daily', 'Backup frequency'),
('session_timeout', '30', 'Session timeout in minutes'),
('max_login_attempts', '5', 'Maximum login attempts'),
('password_min_length', '8', 'Minimum password length'),
('require_password_change', '90', 'Password change required in days');

-- Update existing classes to have default semester if NULL
UPDATE classes SET semester = 'Fall' WHERE semester IS NULL;

-- Display success message
SELECT 'Database migration completed successfully!' as message;
SELECT 'All missing columns have been added.' as info;
