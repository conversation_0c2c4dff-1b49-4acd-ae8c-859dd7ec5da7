# Student Management System Guide

## Overview
The Student Management System provides comprehensive features for managing students in the attendance module. This includes adding individual students, bulk importing via CSV, automatic credential generation, and enrollment management.

## Features

### 1. Add Individual Students
- Navigate to **Admin Dashboard** > **Manage Students**
- Click the **"Add Student"** button
- Fill in student details:
  - Full Name (Required)
  - Department (Required)
  - Phone (Optional)
  - Address (Optional)
- Choose credential generation:
  - **Auto-generate** (Default): System generates unique email and password
  - **Manual**: Enter custom username, email, and password

### 2. Bulk Import Students via CSV
- Click the **"Import Students"** button
- Download the sample CSV template
- Prepare your CSV file with the following columns:
  1. **Full Name** (Required)
  2. **Department Code** (Required - CS, IT, MS, EC)
  3. **Course Code** (Optional - for auto-enrollment)
  4. **Class Section** (Optional - for auto-enrollment)
  5. **Phone** (Optional)
  6. **Address** (Optional)
- Upload the CSV file
- System will automatically:
  - Generate unique emails (format: <EMAIL>)
  - Generate secure passwords
  - Create student accounts
  - Enroll students in specified classes (if provided)

### 3. Generated Credentials

#### Email Generation Logic
- Format: `<EMAIL>`
- If duplicate exists, adds number: `<EMAIL>`
- Special characters are removed from names

#### Password Generation
- 8 characters long
- Mix of uppercase, lowercase, numbers, and special characters
- Example: `Kx9@mP2!`

#### Credential Display
- After adding a single student: Credentials shown on screen
- After bulk import: All credentials displayed in a table
- Can copy individual credentials or download all as CSV

### 4. Student Details Management
- Click **"View Details"** on any student
- Update student information:
  - Full Name
  - Email
  - Phone
  - Address
  - Active/Inactive status
- View enrollment history
- Check attendance summary
- Reset student password

### 5. Class Enrollment
- From the students list, click **"Enroll"**
- Select the class from dropdown
- Students can be enrolled in multiple classes
- Track enrollment status (Active, Dropped, Completed)

### 6. Student Authentication
Students can login using:
- **Username**: Auto-generated (e.g., john.doe123)
- **Email**: Auto-generated or custom
- **Password**: Generated or set by admin

## Security Features
- Passwords are hashed using bcrypt
- Unique constraint on usernames and emails
- Role-based access control
- Session management

## Best Practices

### For CSV Import
1. Verify department codes match existing departments
2. Ensure course codes and sections exist before import
3. Keep a backup of generated credentials
4. Test with small batches first

### For Credential Management
1. Share credentials securely with students
2. Encourage password changes on first login
3. Keep records of distributed credentials
4. Use the download feature for bulk credential distribution

## Troubleshooting

### Common Issues
1. **Import fails**: Check department codes are valid
2. **Duplicate email**: System auto-handles by adding numbers
3. **Enrollment fails**: Verify class exists and is active
4. **Login issues**: Check if student account is active

### Database Tables Involved
- `users`: Stores all student information
- `enrollments`: Tracks class enrollments
- `attendance_records`: Stores attendance data
- `departments`: Department information
- `courses`: Course details
- `classes`: Class sections

## API Endpoints (Internal)
- `POST /admin/students.php?action=add_student`
- `POST /admin/students.php?action=import_students`
- `POST /admin/students.php?action=enroll_student`
- `POST /admin/student-details.php?action=update_student`
- `POST /admin/student-details.php?action=reset_password`

## Future Enhancements
- Email notification system for credentials
- Batch enrollment updates
- Student photo upload
- Parent/Guardian information
- Academic history tracking
- Export student data
- Advanced search and filters
