<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Department Management';
$success = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if ($_POST['action'] === 'add_department') {
        $name = trim($_POST['name']);
        $code = trim($_POST['code']);
        $description = trim($_POST['description']);

        if (!empty($name) && !empty($code)) {
            try {
                // Check if code already exists
                $stmt = $db->prepare("SELECT id FROM departments WHERE code = :code");
                $stmt->bindParam(':code', $code);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    $error = "Department code already exists";
                } else {
                    // Insert new department
                    $stmt = $db->prepare("
                        INSERT INTO departments (name, code, description, created_at)
                        VALUES (:name, :code, :description, NOW())
                    ");
                    $stmt->bindParam(':name', $name);
                    $stmt->bindParam(':code', $code);
                    $stmt->bindParam(':description', $description);
                    $stmt->execute();

                    $success = "Department added successfully!";
                }
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        } else {
            $error = "Please fill in all required fields";
        }
    } elseif ($_POST['action'] === 'edit_department') {
        $id = $_POST['department_id'];
        $name = trim($_POST['name']);
        $code = trim($_POST['code']);
        $description = trim($_POST['description']);

        if (!empty($name) && !empty($code)) {
            try {
                // Check if code already exists for other departments
                $stmt = $db->prepare("SELECT id FROM departments WHERE code = :code AND id != :id");
                $stmt->bindParam(':code', $code);
                $stmt->bindParam(':id', $id);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    $error = "Department code already exists";
                } else {
                    // Update department
                    $stmt = $db->prepare("
                        UPDATE departments
                        SET name = :name, code = :code, description = :description
                        WHERE id = :id
                    ");
                    $stmt->bindParam(':name', $name);
                    $stmt->bindParam(':code', $code);
                    $stmt->bindParam(':description', $description);
                    $stmt->bindParam(':id', $id);
                    $stmt->execute();

                    $success = "Department updated successfully!";
                }
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        } else {
            $error = "Please fill in all required fields";
        }
    } elseif ($_POST['action'] === 'delete_department') {
        $id = $_POST['department_id'];

        try {
            // Check if department has courses
            $stmt = $db->prepare("SELECT COUNT(*) FROM courses WHERE department_id = :id");
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            $course_count = $stmt->fetchColumn();

            if ($course_count > 0) {
                $error = "Cannot delete department. It has $course_count associated courses.";
            } else {
                // Delete department
                $stmt = $db->prepare("DELETE FROM departments WHERE id = :id");
                $stmt->bindParam(':id', $id);
                $stmt->execute();

                $success = "Department deleted successfully!";
            }
        } catch (PDOException $e) {
            $error = "Error deleting department: " . $e->getMessage();
        }
    }
}

// Get all departments with course count
try {
    $stmt = $db->query("
        SELECT d.*, COUNT(c.id) as course_count
        FROM departments d
        LEFT JOIN courses c ON d.id = c.department_id
        GROUP BY d.id
        ORDER BY d.name
    ");
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching departments: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Department Management</h1>
            <p class="mt-2 text-gray-600">Manage academic departments</p>
        </div>
        <button onclick="toggleAddModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
            <i class="fas fa-plus mr-2"></i>Add Department
        </button>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md alert-auto-hide">
            <div class="flex">
                <i class="fas fa-check-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($success); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <!-- Departments Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php foreach ($departments as $dept): ?>
            <div class="bg-white shadow-lg rounded-lg p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($dept['name']); ?></h3>
                        <p class="text-sm text-gray-500 font-mono"><?php echo htmlspecialchars($dept['code']); ?></p>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="editDepartment(<?php echo $dept['id']; ?>, '<?php echo htmlspecialchars($dept['name'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($dept['code'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($dept['description'] ?? '', ENT_QUOTES); ?>')"
                                class="text-indigo-600 hover:text-indigo-900">
                            <i class="fas fa-edit"></i>
                        </button>
                        <?php if ($dept['course_count'] == 0): ?>
                            <form method="POST" class="inline">
                                <input type="hidden" name="action" value="delete_department">
                                <input type="hidden" name="department_id" value="<?php echo $dept['id']; ?>">
                                <button type="submit" class="text-red-600 hover:text-red-900"
                                        onclick="return confirmAction('Are you sure you want to delete this department?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if (isset($dept['description']) && $dept['description']): ?>
                    <p class="text-gray-600 text-sm mb-4"><?php echo htmlspecialchars($dept['description']); ?></p>
                <?php endif; ?>

                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500">
                        <i class="fas fa-book mr-1"></i>
                        <?php echo $dept['course_count']; ?> Course<?php echo $dept['course_count'] != 1 ? 's' : ''; ?>
                    </span>
                    <a href="courses.php?department_id=<?php echo $dept['id']; ?>" class="text-indigo-600 hover:text-indigo-900">
                        View Courses <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<!-- Add/Edit Department Modal -->
<div id="departmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">Add Department</h3>
            </div>
            <form method="POST" class="p-6">
                <input type="hidden" id="modalAction" name="action" value="add_department">
                <input type="hidden" id="departmentId" name="department_id" value="">

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Department Name</label>
                        <input type="text" id="departmentName" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Department Code</label>
                        <input type="text" id="departmentCode" name="code" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea id="departmentDescription" name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="toggleAddModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" id="submitBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        Add Department
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleAddModal() {
    const modal = document.getElementById('departmentModal');
    modal.classList.toggle('hidden');

    // Reset form for add mode
    document.getElementById('modalTitle').textContent = 'Add Department';
    document.getElementById('modalAction').value = 'add_department';
    document.getElementById('submitBtn').textContent = 'Add Department';
    document.getElementById('departmentId').value = '';
    document.getElementById('departmentName').value = '';
    document.getElementById('departmentCode').value = '';
    document.getElementById('departmentDescription').value = '';
}

function editDepartment(id, name, code, description) {
    const modal = document.getElementById('departmentModal');
    modal.classList.remove('hidden');

    // Set form for edit mode
    document.getElementById('modalTitle').textContent = 'Edit Department';
    document.getElementById('modalAction').value = 'edit_department';
    document.getElementById('submitBtn').textContent = 'Update Department';
    document.getElementById('departmentId').value = id;
    document.getElementById('departmentName').value = name;
    document.getElementById('departmentCode').value = code;
    document.getElementById('departmentDescription').value = description;
}
</script>

<?php include '../includes/footer.php'; ?>
