<?php
session_start();
require_once '../../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../../auth/login.php');
    exit();
}

$page_title = 'Student Reports';
$error = '';

// Get filter parameters
$class_filter = $_GET['class_id'] ?? '';
$department_filter = $_GET['department_id'] ?? '';
$attendance_filter = $_GET['attendance_filter'] ?? '';

// Get departments for filter
try {
    $stmt = $db->query("SELECT id, name FROM departments ORDER BY name");
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching departments: " . $e->getMessage();
}

// Get classes for filter
try {
    $where_clause = '';
    if ($department_filter) {
        $where_clause = 'WHERE c.department_id = ' . intval($department_filter);
    }
    
    $stmt = $db->query("
        SELECT cl.id, c.name as course_name, cl.section, d.name as department_name
        FROM classes cl
        JOIN courses c ON cl.course_id = c.id
        JOIN departments d ON c.department_id = d.id
        $where_clause
        ORDER BY d.name, c.name, cl.section
    ");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching classes: " . $e->getMessage();
}

// Build query for student data
$where_conditions = ["u.role = 'student'", "u.is_active = 1"];
$params = [];

if ($class_filter) {
    $where_conditions[] = "e.class_id = :class_id";
    $params[':class_id'] = $class_filter;
}

if ($department_filter && !$class_filter) {
    $where_conditions[] = "d.id = :department_id";
    $params[':department_id'] = $department_filter;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get student data with attendance statistics
try {
    $base_query = "
        SELECT 
            u.id,
            u.full_name,
            u.username,
            u.email,
            u.phone,
            d.name as department_name,
            c.name as course_name,
            cl.section,
            COUNT(DISTINCT ats.id) as total_sessions,
            COUNT(ar.id) as attended_sessions,
            SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) as present_count,
            SUM(CASE WHEN ar.status = 'absent' THEN 1 ELSE 0 END) as absent_count,
            SUM(CASE WHEN ar.status = 'late' THEN 1 ELSE 0 END) as late_count,
            CASE 
                WHEN COUNT(DISTINCT ats.id) > 0 THEN 
                    ROUND((SUM(CASE WHEN ar.status IN ('present', 'late') THEN 1 ELSE 0 END) / COUNT(DISTINCT ats.id)) * 100, 1)
                ELSE 0 
            END as attendance_percentage
        FROM users u
        LEFT JOIN enrollments e ON u.id = e.student_id AND e.status = 'active'
        LEFT JOIN classes cl ON e.class_id = cl.id
        LEFT JOIN courses c ON cl.course_id = c.id
        LEFT JOIN departments d ON c.department_id = d.id
        LEFT JOIN attendance_sessions ats ON cl.id = ats.class_id
        LEFT JOIN attendance_records ar ON ats.id = ar.session_id AND ar.student_id = u.id
        $where_clause
        GROUP BY u.id, cl.id
    ";
    
    // Apply attendance filter if specified
    if ($attendance_filter) {
        switch ($attendance_filter) {
            case 'low':
                $base_query .= " HAVING attendance_percentage < 75";
                break;
            case 'medium':
                $base_query .= " HAVING attendance_percentage >= 75 AND attendance_percentage < 85";
                break;
            case 'high':
                $base_query .= " HAVING attendance_percentage >= 85";
                break;
        }
    }
    
    $base_query .= " ORDER BY u.full_name, c.name";
    
    $stmt = $db->prepare($base_query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error = "Error fetching student data: " . $e->getMessage();
}

include '../../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Student Reports</h1>
        <p class="mt-2 text-gray-600">View student enrollment and attendance statistics</p>
    </div>

    <!-- Filters -->
    <div class="mb-6 bg-white p-6 rounded-lg shadow">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                <select name="department_id" onchange="this.form.submit()" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="">All Departments</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" <?php echo $department_filter == $dept['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($dept['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Class</label>
                <select name="class_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="">All Classes</option>
                    <?php foreach ($classes as $class): ?>
                        <option value="<?php echo $class['id']; ?>" <?php echo $class_filter == $class['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($class['course_name'] . ' - ' . $class['section']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Attendance Level</label>
                <select name="attendance_filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="">All Students</option>
                    <option value="low" <?php echo $attendance_filter === 'low' ? 'selected' : ''; ?>>Low (&lt; 75%)</option>
                    <option value="medium" <?php echo $attendance_filter === 'medium' ? 'selected' : ''; ?>>Medium (75-85%)</option>
                    <option value="high" <?php echo $attendance_filter === 'high' ? 'selected' : ''; ?>>High (&gt; 85%)</option>
                </select>
            </div>
            
            <div class="flex items-end">
                <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
            </div>
        </form>
    </div>

    <!-- Error Message -->
    <?php if ($error): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <!-- Students Table -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">Student List</h3>
            <?php if (!empty($students)): ?>
                <button onclick="exportToCSV()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm">
                    <i class="fas fa-download mr-2"></i>Export CSV
                </button>
            <?php endif; ?>
        </div>
        
        <?php if (!empty($students)): ?>
            <div class="overflow-x-auto">
                <table id="studentsTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($students as $student): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($student['full_name']); ?></div>
                                    <div class="text-sm text-gray-500">@<?php echo htmlspecialchars($student['username']); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($student['course_name']): ?>
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($student['course_name']); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($student['section']); ?> - <?php echo htmlspecialchars($student['department_name']); ?></div>
                                    <?php else: ?>
                                        <span class="text-sm text-gray-400">Not enrolled</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo htmlspecialchars($student['email']); ?></div>
                                    <?php if ($student['phone']): ?>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($student['phone']); ?></div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $student['attended_sessions']; ?> / <?php echo $student['total_sessions']; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex space-x-1">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            P: <?php echo $student['present_count']; ?>
                                        </span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                            A: <?php echo $student['absent_count']; ?>
                                        </span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            L: <?php echo $student['late_count']; ?>
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="text-sm font-medium text-gray-900 mr-2"><?php echo $student['attendance_percentage']; ?>%</div>
                                        <div class="w-16 bg-gray-200 rounded-full h-2">
                                            <div class="h-2 rounded-full <?php 
                                                echo $student['attendance_percentage'] >= 85 ? 'bg-green-500' : 
                                                    ($student['attendance_percentage'] >= 75 ? 'bg-yellow-500' : 'bg-red-500'); 
                                            ?>" style="width: <?php echo min(100, $student['attendance_percentage']); ?>%"></div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <i class="fas fa-user-graduate text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">No students found for the selected criteria.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function exportToCSV() {
    const table = document.getElementById('studentsTable');
    const rows = table.querySelectorAll('tr');
    let csv = [];
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cols = row.querySelectorAll('td, th');
        let csvRow = [];
        
        for (let j = 0; j < cols.length; j++) {
            csvRow.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
        }
        
        csv.push(csvRow.join(','));
    }
    
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'student_report_' + new Date().toISOString().split('T')[0] + '.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>

<?php include '../../includes/footer.php'; ?>
