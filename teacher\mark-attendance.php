<?php
session_start();
require_once '../config/database.php';

// Check if user is teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Mark Attendance';
$teacher_id = $_SESSION['user_id'];
$success = '';
$error = '';

// Get teacher's classes
try {
    $stmt = $db->prepare("
        SELECT 
            cl.id,
            cl.section,
            cl.academic_year,
            c.name as course_name,
            c.code as course_code,
            d.name as department_name
        FROM classes cl
        JOIN courses c ON cl.course_id = c.id
        JOIN departments d ON c.department_id = d.id
        WHERE cl.teacher_id = :teacher_id
        ORDER BY c.name, cl.section
    ");
    $stmt->bindParam(':teacher_id', $teacher_id);
    $stmt->execute();
    $teacher_classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

// Handle form submission
if ($_POST && isset($_POST['action'])) {
    if ($_POST['action'] === 'create_session') {
        $class_id = $_POST['class_id'];
        $date = $_POST['date'];
        $start_time = $_POST['start_time'];
        $end_time = $_POST['end_time'];
        $topic = trim($_POST['topic']);
        
        try {
            // Create attendance session
            $stmt = $db->prepare("
                INSERT INTO attendance_sessions (class_id, date, start_time, end_time, topic, created_by)
                VALUES (:class_id, :date, :start_time, :end_time, :topic, :created_by)
            ");
            $stmt->bindParam(':class_id', $class_id);
            $stmt->bindParam(':date', $date);
            $stmt->bindParam(':start_time', $start_time);
            $stmt->bindParam(':end_time', $end_time);
            $stmt->bindParam(':topic', $topic);
            $stmt->bindParam(':created_by', $teacher_id);
            $stmt->execute();
            
            $session_id = $db->lastInsertId();
            $success = "Attendance session created successfully!";
            
            // Redirect to mark attendance for this session
            header("Location: mark-attendance.php?session_id=$session_id");
            exit();
            
        } catch (PDOException $e) {
            $error = "Error creating session: " . $e->getMessage();
        }
    } elseif ($_POST['action'] === 'mark_attendance') {
        $session_id = $_POST['session_id'];
        $attendance_data = $_POST['attendance'] ?? [];
        
        try {
            $db->beginTransaction();
            
            // Delete existing records for this session
            $stmt = $db->prepare("DELETE FROM attendance_records WHERE session_id = :session_id");
            $stmt->bindParam(':session_id', $session_id);
            $stmt->execute();
            
            // Insert new attendance records
            $stmt = $db->prepare("
                INSERT INTO attendance_records (session_id, student_id, status, marked_by, remarks)
                VALUES (:session_id, :student_id, :status, :marked_by, :remarks)
            ");
            
            foreach ($attendance_data as $student_id => $data) {
                $status = $data['status'];
                $remarks = trim($data['remarks'] ?? '');
                
                $stmt->bindParam(':session_id', $session_id);
                $stmt->bindParam(':student_id', $student_id);
                $stmt->bindParam(':status', $status);
                $stmt->bindParam(':marked_by', $teacher_id);
                $stmt->bindParam(':remarks', $remarks);
                $stmt->execute();
            }
            
            $db->commit();
            $success = "Attendance marked successfully!";
            
        } catch (PDOException $e) {
            $db->rollBack();
            $error = "Error marking attendance: " . $e->getMessage();
        }
    }
}

// Get session details if session_id is provided
$session_details = null;
$students = [];
$existing_attendance = [];

if (isset($_GET['session_id'])) {
    $session_id = $_GET['session_id'];
    
    try {
        // Get session details
        $stmt = $db->prepare("
            SELECT 
                ats.*,
                c.name as course_name,
                c.code as course_code,
                cl.section
            FROM attendance_sessions ats
            JOIN classes cl ON ats.class_id = cl.id
            JOIN courses c ON cl.course_id = c.id
            WHERE ats.id = :session_id AND cl.teacher_id = :teacher_id
        ");
        $stmt->bindParam(':session_id', $session_id);
        $stmt->bindParam(':teacher_id', $teacher_id);
        $stmt->execute();
        $session_details = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($session_details) {
            // Get enrolled students
            $stmt = $db->prepare("
                SELECT 
                    u.id,
                    u.username,
                    u.full_name,
                    u.email
                FROM enrollments e
                JOIN users u ON e.student_id = u.id
                WHERE e.class_id = :class_id AND e.status = 'active'
                ORDER BY u.full_name
            ");
            $stmt->bindParam(':class_id', $session_details['class_id']);
            $stmt->execute();
            $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get existing attendance records
            $stmt = $db->prepare("
                SELECT student_id, status, remarks
                FROM attendance_records
                WHERE session_id = :session_id
            ");
            $stmt->bindParam(':session_id', $session_id);
            $stmt->execute();
            $existing_attendance = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        }
    } catch (PDOException $e) {
        $error = "Database error: " . $e->getMessage();
    }
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Mark Attendance</h1>
        <p class="mt-2 text-gray-600">Create attendance sessions and mark student attendance</p>
    </div>

    <?php if ($success): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md alert-auto-hide">
            <div class="flex">
                <i class="fas fa-check-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($success); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if (!isset($_GET['session_id'])): ?>
        <!-- Create New Session -->
        <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-plus-circle mr-2 text-indigo-600"></i>
                Create New Attendance Session
            </h3>
            
            <form method="POST" class="space-y-6">
                <input type="hidden" name="action" value="create_session">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="class_id" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-chalkboard mr-2"></i>Select Class
                        </label>
                        <select id="class_id" name="class_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">Choose a class...</option>
                            <?php foreach ($teacher_classes as $class): ?>
                                <option value="<?php echo $class['id']; ?>">
                                    <?php echo htmlspecialchars($class['course_name']); ?> - Section <?php echo htmlspecialchars($class['section']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label for="date" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-calendar mr-2"></i>Date
                        </label>
                        <input type="date" id="date" name="date" required value="<?php echo date('Y-m-d'); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    
                    <div>
                        <label for="start_time" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-clock mr-2"></i>Start Time
                        </label>
                        <input type="time" id="start_time" name="start_time" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    
                    <div>
                        <label for="end_time" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-clock mr-2"></i>End Time
                        </label>
                        <input type="time" id="end_time" name="end_time" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                </div>
                
                <div>
                    <label for="topic" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-book mr-2"></i>Topic/Subject (Optional)
                    </label>
                    <input type="text" id="topic" name="topic" placeholder="Enter the topic covered in this session" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-plus mr-2"></i>
                        Create Session
                    </button>
                </div>
            </form>
        </div>
    <?php else: ?>
        <!-- Mark Attendance for Session -->
        <?php if ($session_details): ?>
            <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-clipboard-check mr-2 text-indigo-600"></i>
                            Mark Attendance
                        </h3>
                        <p class="text-sm text-gray-600 mt-1">
                            <?php echo htmlspecialchars($session_details['course_name']); ?> - Section <?php echo htmlspecialchars($session_details['section']); ?>
                        </p>
                        <p class="text-sm text-gray-600">
                            <?php echo date('M d, Y', strtotime($session_details['date'])); ?> | 
                            <?php echo date('h:i A', strtotime($session_details['start_time'])); ?> - 
                            <?php echo date('h:i A', strtotime($session_details['end_time'])); ?>
                        </p>
                        <?php if ($session_details['topic']): ?>
                            <p class="text-sm text-gray-600">Topic: <?php echo htmlspecialchars($session_details['topic']); ?></p>
                        <?php endif; ?>
                    </div>
                    <a href="mark-attendance.php" class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Create Session
                    </a>
                </div>
                
                <?php if (!empty($students)): ?>
                    <form method="POST" class="space-y-4">
                        <input type="hidden" name="action" value="mark_attendance">
                        <input type="hidden" name="session_id" value="<?php echo $session_details['id']; ?>">
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Present</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Absent</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Late</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php foreach ($students as $student): ?>
                                        <?php $current_status = $existing_attendance[$student['id']] ?? 'present'; ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="h-8 w-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                                                        <i class="fas fa-user text-indigo-600 text-sm"></i>
                                                    </div>
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($student['full_name']); ?></div>
                                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($student['username']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <input type="radio" name="attendance[<?php echo $student['id']; ?>][status]" value="present" <?php echo $current_status === 'present' ? 'checked' : ''; ?> class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300">
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <input type="radio" name="attendance[<?php echo $student['id']; ?>][status]" value="absent" <?php echo $current_status === 'absent' ? 'checked' : ''; ?> class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300">
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <input type="radio" name="attendance[<?php echo $student['id']; ?>][status]" value="late" <?php echo $current_status === 'late' ? 'checked' : ''; ?> class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300">
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="text" name="attendance[<?php echo $student['id']; ?>][remarks]" placeholder="Optional remarks" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="flex justify-between items-center pt-6">
                            <div class="text-sm text-gray-600">
                                Total Students: <?php echo count($students); ?>
                            </div>
                            <div class="flex space-x-3">
                                <button type="button" onclick="markAll('present')" class="inline-flex items-center px-3 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100">
                                    <i class="fas fa-check mr-2"></i>
                                    Mark All Present
                                </button>
                                <button type="button" onclick="markAll('absent')" class="inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100">
                                    <i class="fas fa-times mr-2"></i>
                                    Mark All Absent
                                </button>
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <i class="fas fa-save mr-2"></i>
                                    Save Attendance
                                </button>
                            </div>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-user-graduate text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-500">No students enrolled in this class.</p>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                <div class="flex">
                    <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                    <span>Session not found or you don't have permission to access it.</span>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<script>
function markAll(status) {
    const radios = document.querySelectorAll(`input[type="radio"][value="${status}"]`);
    radios.forEach(radio => {
        radio.checked = true;
    });
}
</script>

<?php include '../includes/footer.php'; ?>
