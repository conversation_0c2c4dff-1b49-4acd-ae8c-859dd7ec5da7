<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    die('Access denied. Admin only.');
}

echo "<h2>Testing Student Import System</h2>";

// Test data
$test_students = [
    ['name' => 'Test Student One', 'dept' => 'CS', 'phone' => '9999999991'],
    ['name' => 'Test Student Two', 'dept' => 'IT', 'phone' => '9999999992'],
    ['name' => 'Test Student Three', 'dept' => 'MS', 'phone' => '9999999993'],
];

echo "<h3>Testing Email Generation:</h3>";
echo "<ul>";

// Test email generation function
function generateTestEmail($fullName, $domain = 'sanskar.edu.in') {
    $nameParts = explode(' ', strtolower($fullName));
    $firstName = $nameParts[0];
    $lastName = isset($nameParts[count($nameParts) - 1]) ? $nameParts[count($nameParts) - 1] : '';
    
    $baseEmail = $firstName . '.' . $lastName;
    $baseEmail = preg_replace('/[^a-z0-9.]/', '', $baseEmail);
    
    return $baseEmail . '@' . $domain;
}

foreach ($test_students as $student) {
    $email = generateTestEmail($student['name']);
    echo "<li>{$student['name']} → {$email}</li>";
}

echo "</ul>";

echo "<h3>Testing Password Generation:</h3>";
echo "<ul>";

// Test password generation
function generateTestPassword($length = 8) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%';
    return substr(str_shuffle($chars), 0, $length);
}

for ($i = 0; $i < 5; $i++) {
    $password = generateTestPassword();
    echo "<li>Sample password " . ($i + 1) . ": {$password}</li>";
}

echo "</ul>";

// Check database connectivity
echo "<h3>Database Check:</h3>";
try {
    // Check departments
    $stmt = $db->query("SELECT code, name FROM departments ORDER BY code");
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Available Departments:</p>";
    echo "<ul>";
    foreach ($departments as $dept) {
        echo "<li>{$dept['code']} - {$dept['name']}</li>";
    }
    echo "</ul>";
    
    // Check courses
    $stmt = $db->query("SELECT code, name FROM courses ORDER BY code");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Available Courses:</p>";
    echo "<ul>";
    foreach ($courses as $course) {
        echo "<li>{$course['code']} - {$course['name']}</li>";
    }
    echo "</ul>";
    
    // Check existing students
    $stmt = $db->query("SELECT COUNT(*) FROM users WHERE role = 'student'");
    $studentCount = $stmt->fetchColumn();
    echo "<p>Current number of students: {$studentCount}</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='students.php'>Go to Student Management</a></p>";
echo "<p><a href='dashboard.php'>Back to Dashboard</a></p>";
?>
