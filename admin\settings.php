<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'System Settings';
$success = '';
$error = '';

// Handle form submission
if ($_POST) {
    try {
        $db->beginTransaction();
        
        foreach ($_POST as $key => $value) {
            if ($key !== 'action') {
                // Update or insert setting
                $stmt = $db->prepare("
                    INSERT INTO settings (setting_key, setting_value) 
                    VALUES (:key, :value)
                    ON DUPLICATE KEY UPDATE setting_value = :value
                ");
                $stmt->bindParam(':key', $key);
                $stmt->bindParam(':value', $value);
                $stmt->execute();
            }
        }
        
        $db->commit();
        $success = "Settings updated successfully!";
        
    } catch (PDOException $e) {
        $db->rollBack();
        $error = "Error updating settings: " . $e->getMessage();
    }
}

// Get current settings
$settings = [];
try {
    $stmt = $db->query("SELECT setting_key, setting_value FROM settings");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    $error = "Error fetching settings: " . $e->getMessage();
}

// Default values if settings don't exist
$default_settings = [
    'institute_name' => 'Sanskar Institute of Management & Information Technology',
    'academic_year' => '2024-2025',
    'attendance_threshold' => '75',
    'late_threshold' => '15',
    'timezone' => 'Asia/Kolkata',
    'email_notifications' => '1',
    'sms_notifications' => '0',
    'auto_backup' => '1',
    'backup_frequency' => 'daily',
    'session_timeout' => '30',
    'max_login_attempts' => '5',
    'password_min_length' => '8',
    'require_password_change' => '90'
];

// Merge with current settings
foreach ($default_settings as $key => $default_value) {
    if (!isset($settings[$key])) {
        $settings[$key] = $default_value;
    }
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">System Settings</h1>
        <p class="mt-2 text-gray-600">Configure system-wide settings and preferences</p>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md alert-auto-hide">
            <div class="flex">
                <i class="fas fa-check-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($success); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <form method="POST" class="space-y-8">
        <input type="hidden" name="action" value="update_settings">

        <!-- General Settings -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-cog mr-2 text-indigo-600"></i>
                General Settings
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Institute Name</label>
                    <input type="text" name="institute_name" value="<?php echo htmlspecialchars($settings['institute_name']); ?>" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Academic Year</label>
                    <input type="text" name="academic_year" value="<?php echo htmlspecialchars($settings['academic_year']); ?>" 
                           placeholder="2024-2025"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Timezone</label>
                    <select name="timezone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="Asia/Kolkata" <?php echo $settings['timezone'] === 'Asia/Kolkata' ? 'selected' : ''; ?>>Asia/Kolkata (IST)</option>
                        <option value="UTC" <?php echo $settings['timezone'] === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                        <option value="America/New_York" <?php echo $settings['timezone'] === 'America/New_York' ? 'selected' : ''; ?>>America/New_York (EST)</option>
                        <option value="Europe/London" <?php echo $settings['timezone'] === 'Europe/London' ? 'selected' : ''; ?>>Europe/London (GMT)</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Session Timeout (minutes)</label>
                    <input type="number" name="session_timeout" value="<?php echo htmlspecialchars($settings['session_timeout']); ?>" 
                           min="5" max="120"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
            </div>
        </div>

        <!-- Attendance Settings -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-calendar-check mr-2 text-indigo-600"></i>
                Attendance Settings
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Minimum Attendance Threshold (%)</label>
                    <input type="number" name="attendance_threshold" value="<?php echo htmlspecialchars($settings['attendance_threshold']); ?>" 
                           min="0" max="100"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <p class="text-xs text-gray-500 mt-1">Students below this percentage will be flagged</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Late Threshold (minutes)</label>
                    <input type="number" name="late_threshold" value="<?php echo htmlspecialchars($settings['late_threshold']); ?>" 
                           min="1" max="60"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <p class="text-xs text-gray-500 mt-1">Students arriving after this time will be marked as late</p>
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-shield-alt mr-2 text-indigo-600"></i>
                Security Settings
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Maximum Login Attempts</label>
                    <input type="number" name="max_login_attempts" value="<?php echo htmlspecialchars($settings['max_login_attempts']); ?>" 
                           min="3" max="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Minimum Password Length</label>
                    <input type="number" name="password_min_length" value="<?php echo htmlspecialchars($settings['password_min_length']); ?>" 
                           min="6" max="20"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Password Change Required (days)</label>
                    <input type="number" name="require_password_change" value="<?php echo htmlspecialchars($settings['require_password_change']); ?>" 
                           min="30" max="365"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <p class="text-xs text-gray-500 mt-1">0 = never require password change</p>
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-bell mr-2 text-indigo-600"></i>
                Notification Settings
            </h3>
            <div class="space-y-4">
                <div class="flex items-center">
                    <input type="checkbox" name="email_notifications" value="1" 
                           <?php echo $settings['email_notifications'] ? 'checked' : ''; ?>
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label class="ml-2 block text-sm text-gray-900">Enable Email Notifications</label>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" name="sms_notifications" value="1" 
                           <?php echo $settings['sms_notifications'] ? 'checked' : ''; ?>
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label class="ml-2 block text-sm text-gray-900">Enable SMS Notifications</label>
                </div>
            </div>
        </div>

        <!-- Backup Settings -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-database mr-2 text-indigo-600"></i>
                Backup Settings
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="flex items-center">
                    <input type="checkbox" name="auto_backup" value="1" 
                           <?php echo $settings['auto_backup'] ? 'checked' : ''; ?>
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label class="ml-2 block text-sm text-gray-900">Enable Automatic Backups</label>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Backup Frequency</label>
                    <select name="backup_frequency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="daily" <?php echo $settings['backup_frequency'] === 'daily' ? 'selected' : ''; ?>>Daily</option>
                        <option value="weekly" <?php echo $settings['backup_frequency'] === 'weekly' ? 'selected' : ''; ?>>Weekly</option>
                        <option value="monthly" <?php echo $settings['backup_frequency'] === 'monthly' ? 'selected' : ''; ?>>Monthly</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="flex justify-end">
            <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-md font-medium">
                <i class="fas fa-save mr-2"></i>Save Settings
            </button>
        </div>
    </form>
</div>

<?php include '../includes/footer.php'; ?>
