<?php
session_start();
require_once '../config/database.php';

// Check if user is teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    http_response_code(403);
    echo '<div class="text-center py-8"><p class="text-red-500">Access denied</p></div>';
    exit();
}

$session_id = $_GET['session_id'] ?? '';
$teacher_id = $_SESSION['user_id'];

if (!$session_id) {
    echo '<div class="text-center py-8"><p class="text-red-500">Invalid session ID</p></div>';
    exit();
}

try {
    // Get session details
    $stmt = $db->prepare("
        SELECT 
            ats.*,
            c.name as course_name,
            c.code as course_code,
            cl.section,
            u.full_name as teacher_name
        FROM attendance_sessions ats
        JOIN classes cl ON ats.class_id = cl.id
        JOIN courses c ON cl.course_id = c.id
        JOIN users u ON cl.teacher_id = u.id
        WHERE ats.id = :session_id AND cl.teacher_id = :teacher_id
    ");
    $stmt->bindParam(':session_id', $session_id);
    $stmt->bindParam(':teacher_id', $teacher_id);
    $stmt->execute();
    
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        echo '<div class="text-center py-8"><p class="text-red-500">Session not found</p></div>';
        exit();
    }
    
    // Get attendance records for this session
    $stmt = $db->prepare("
        SELECT 
            u.full_name as student_name,
            u.username as student_username,
            ar.status,
            ar.remarks,
            ar.marked_at
        FROM enrollments e
        JOIN users u ON e.student_id = u.id
        LEFT JOIN attendance_records ar ON e.student_id = ar.student_id AND ar.session_id = :session_id
        WHERE e.class_id = :class_id AND e.status = 'active'
        ORDER BY u.full_name
    ");
    $stmt->bindParam(':session_id', $session_id);
    $stmt->bindParam(':class_id', $session['class_id']);
    $stmt->execute();
    
    $attendance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    echo '<div class="text-center py-8"><p class="text-red-500">Database error: ' . htmlspecialchars($e->getMessage()) . '</p></div>';
    exit();
}
?>

<div class="space-y-6">
    <!-- Session Information -->
    <div class="bg-gray-50 p-4 rounded-lg">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">Session Information</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="text-sm text-gray-600">Course</p>
                <p class="font-medium"><?php echo htmlspecialchars($session['course_name']); ?></p>
                <p class="text-sm text-gray-500"><?php echo htmlspecialchars($session['course_code'] . ' - ' . $session['section']); ?></p>
            </div>
            <div>
                <p class="text-sm text-gray-600">Date & Time</p>
                <p class="font-medium"><?php echo date('M d, Y', strtotime($session['date'])); ?></p>
                <p class="text-sm text-gray-500">
                    <?php echo date('h:i A', strtotime($session['start_time'])) . ' - ' . date('h:i A', strtotime($session['end_time'])); ?>
                </p>
            </div>
            <?php if ($session['topic']): ?>
                <div class="md:col-span-2">
                    <p class="text-sm text-gray-600">Topic</p>
                    <p class="font-medium"><?php echo htmlspecialchars($session['topic']); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Attendance Records -->
    <div>
        <h4 class="text-lg font-semibold text-gray-900 mb-3">Student Attendance</h4>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marked At</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($attendance_records as $record): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($record['student_name']); ?></div>
                                <div class="text-sm text-gray-500">@<?php echo htmlspecialchars($record['student_username']); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if ($record['status']): ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        <?php echo $record['status'] === 'present' ? 'bg-green-100 text-green-800' : 
                                            ($record['status'] === 'late' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'); ?>">
                                        <?php echo ucfirst($record['status']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                        Not Marked
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <?php echo $record['remarks'] ? htmlspecialchars($record['remarks']) : '-'; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $record['marked_at'] ? date('M d, Y h:i A', strtotime($record['marked_at'])) : '-'; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="bg-blue-50 p-4 rounded-lg">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">Summary</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <?php
            $total_students = count($attendance_records);
            $present_count = 0;
            $absent_count = 0;
            $late_count = 0;
            $unmarked_count = 0;
            
            foreach ($attendance_records as $record) {
                if ($record['status']) {
                    switch ($record['status']) {
                        case 'present':
                            $present_count++;
                            break;
                        case 'absent':
                            $absent_count++;
                            break;
                        case 'late':
                            $late_count++;
                            break;
                    }
                } else {
                    $unmarked_count++;
                }
            }
            
            $attendance_percentage = $total_students > 0 ? 
                round((($present_count + $late_count) / $total_students) * 100, 1) : 0;
            ?>
            
            <div class="text-center">
                <p class="text-2xl font-bold text-blue-600"><?php echo $total_students; ?></p>
                <p class="text-sm text-gray-600">Total Students</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600"><?php echo $present_count; ?></p>
                <p class="text-sm text-gray-600">Present</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-red-600"><?php echo $absent_count; ?></p>
                <p class="text-sm text-gray-600">Absent</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-yellow-600"><?php echo $late_count; ?></p>
                <p class="text-sm text-gray-600">Late</p>
            </div>
        </div>
        
        <?php if ($unmarked_count > 0): ?>
            <div class="mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded-md">
                <p class="text-sm text-yellow-800">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <?php echo $unmarked_count; ?> student<?php echo $unmarked_count > 1 ? 's' : ''; ?> not marked yet.
                </p>
            </div>
        <?php endif; ?>
        
        <div class="mt-4 text-center">
            <p class="text-lg font-semibold text-gray-900">
                Attendance Rate: <span class="text-blue-600"><?php echo $attendance_percentage; ?>%</span>
            </p>
        </div>
    </div>
</div>
