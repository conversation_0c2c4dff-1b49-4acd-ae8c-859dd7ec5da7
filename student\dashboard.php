<?php
session_start();
require_once '../config/database.php';

// Check if user is student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Student Dashboard';
$student_id = $_SESSION['user_id'];

// Get student's statistics
try {
    // Get student's enrolled classes
    $stmt = $db->prepare("
        SELECT 
            cl.id,
            cl.section,
            cl.academic_year,
            c.name as course_name,
            c.code as course_code,
            c.credits,
            d.name as department_name,
            u.full_name as teacher_name
        FROM enrollments e
        JOIN classes cl ON e.class_id = cl.id
        JOIN courses c ON cl.course_id = c.id
        JOIN departments d ON c.department_id = d.id
        JOIN users u ON cl.teacher_id = u.id
        WHERE e.student_id = :student_id AND e.status = 'active'
        ORDER BY c.name
    ");
    $stmt->bindParam(':student_id', $student_id);
    $stmt->execute();
    $enrolled_classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get attendance statistics
    $stmt = $db->prepare("
        SELECT 
            COUNT(DISTINCT ats.id) as total_sessions,
            COUNT(ar.id) as attended_sessions,
            SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) as present_count,
            SUM(CASE WHEN ar.status = 'absent' THEN 1 ELSE 0 END) as absent_count,
            SUM(CASE WHEN ar.status = 'late' THEN 1 ELSE 0 END) as late_count
        FROM enrollments e
        JOIN classes cl ON e.class_id = cl.id
        JOIN attendance_sessions ats ON cl.id = ats.class_id
        LEFT JOIN attendance_records ar ON ats.id = ar.session_id AND ar.student_id = :student_id
        WHERE e.student_id = :student_id AND e.status = 'active'
    ");
    $stmt->bindParam(':student_id', $student_id);
    $stmt->execute();
    $attendance_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Calculate attendance percentage
    $attendance_percentage = 0;
    if ($attendance_stats['attended_sessions'] > 0) {
        $attendance_percentage = round(($attendance_stats['present_count'] / $attendance_stats['attended_sessions']) * 100, 1);
    }
    
    // Get recent attendance records
    $stmt = $db->prepare("
        SELECT 
            ats.date,
            ats.start_time,
            ats.end_time,
            ats.topic,
            c.name as course_name,
            cl.section,
            ar.status,
            ar.marked_at
        FROM attendance_records ar
        JOIN attendance_sessions ats ON ar.session_id = ats.id
        JOIN classes cl ON ats.class_id = cl.id
        JOIN courses c ON cl.course_id = c.id
        WHERE ar.student_id = :student_id
        ORDER BY ats.date DESC, ats.start_time DESC
        LIMIT 10
    ");
    $stmt->bindParam(':student_id', $student_id);
    $stmt->execute();
    $recent_attendance = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get class-wise attendance
    $stmt = $db->prepare("
        SELECT 
            c.name as course_name,
            c.code as course_code,
            cl.section,
            COUNT(DISTINCT ats.id) as total_sessions,
            COUNT(ar.id) as attended_sessions,
            SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) as present_count,
            CASE 
                WHEN COUNT(ar.id) > 0 THEN ROUND((SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) / COUNT(ar.id)) * 100, 1)
                ELSE 0 
            END as attendance_percentage
        FROM enrollments e
        JOIN classes cl ON e.class_id = cl.id
        JOIN courses c ON cl.course_id = c.id
        JOIN attendance_sessions ats ON cl.id = ats.class_id
        LEFT JOIN attendance_records ar ON ats.id = ar.session_id AND ar.student_id = :student_id
        WHERE e.student_id = :student_id AND e.status = 'active'
        GROUP BY cl.id
        ORDER BY c.name
    ");
    $stmt->bindParam(':student_id', $student_id);
    $stmt->execute();
    $class_attendance = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Student Dashboard</h1>
        <p class="mt-2 text-gray-600">Welcome back, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <!-- Enrolled Classes -->
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-book text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Enrolled Classes</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($enrolled_classes); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overall Attendance -->
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-percentage text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Overall Attendance</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $attendance_percentage; ?>%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Present Days -->
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Present</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $attendance_stats['present_count'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Absent Days -->
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-times text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Absent</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $attendance_stats['absent_count'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Attendance Overview -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-chart-pie mr-2 text-indigo-600"></i>
                Attendance Overview
            </h3>
            <div class="space-y-3">
                <a href="my-attendance.php" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition duration-200">
                    <i class="fas fa-calendar-check text-blue-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-blue-900 block">View My Attendance</span>
                        <span class="text-xs text-blue-700">Check detailed attendance records</span>
                    </div>
                </a>
                <a href="attendance-reports.php" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition duration-200">
                    <i class="fas fa-chart-bar text-green-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-green-900 block">Attendance Reports</span>
                        <span class="text-xs text-green-700">Generate attendance reports</span>
                    </div>
                </a>
                <a href="class-schedule.php" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition duration-200">
                    <i class="fas fa-calendar text-purple-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-purple-900 block">Class Schedule</span>
                        <span class="text-xs text-purple-700">View upcoming classes</span>
                    </div>
                </a>
            </div>
        </div>

        <!-- Academic Information -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-graduation-cap mr-2 text-indigo-600"></i>
                Academic Information
            </h3>
            <div class="space-y-3">
                <a href="my-classes.php" class="flex items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition duration-200">
                    <i class="fas fa-chalkboard text-orange-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-orange-900 block">My Classes</span>
                        <span class="text-xs text-orange-700">View enrolled courses</span>
                    </div>
                </a>
                <a href="teachers.php" class="flex items-center p-4 bg-red-50 rounded-lg hover:bg-red-100 transition duration-200">
                    <i class="fas fa-chalkboard-teacher text-red-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-red-900 block">My Teachers</span>
                        <span class="text-xs text-red-700">View teacher information</span>
                    </div>
                </a>
                <a href="profile.php" class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200">
                    <i class="fas fa-user-cog text-gray-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-gray-900 block">My Profile</span>
                        <span class="text-xs text-gray-700">Update profile information</span>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- Class-wise Attendance -->
    <div class="bg-white shadow-lg rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-chart-line mr-2 text-indigo-600"></i>
                Class-wise Attendance
            </h3>
        </div>
        <div class="p-6">
            <?php if (!empty($class_attendance)): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php foreach ($class_attendance as $class): ?>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex-1">
                                    <h4 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($class['course_name']); ?></h4>
                                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($class['course_code']); ?> - Section <?php echo htmlspecialchars($class['section']); ?></p>
                                </div>
                                <div class="text-right">
                                    <span class="text-2xl font-bold <?php echo $class['attendance_percentage'] >= 75 ? 'text-green-600' : ($class['attendance_percentage'] >= 60 ? 'text-yellow-600' : 'text-red-600'); ?>">
                                        <?php echo $class['attendance_percentage']; ?>%
                                    </span>
                                </div>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                                <div class="<?php echo $class['attendance_percentage'] >= 75 ? 'bg-green-600' : ($class['attendance_percentage'] >= 60 ? 'bg-yellow-600' : 'bg-red-600'); ?> h-2 rounded-full" style="width: <?php echo $class['attendance_percentage']; ?>%"></div>
                            </div>
                            <div class="flex justify-between text-sm text-gray-600">
                                <span>Present: <?php echo $class['present_count']; ?></span>
                                <span>Total: <?php echo $class['attended_sessions']; ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-chart-line text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500">No attendance data available yet.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Attendance -->
    <div class="bg-white shadow-lg rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-clock mr-2 text-indigo-600"></i>
                Recent Attendance Records
            </h3>
        </div>
        <div class="p-6">
            <?php if (!empty($recent_attendance)): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($recent_attendance as $record): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('M d, Y', strtotime($record['date'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('h:i A', strtotime($record['start_time'])) . ' - ' . date('h:i A', strtotime($record['end_time'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($record['course_name']); ?> - <?php echo htmlspecialchars($record['section']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($record['topic'] ?: 'N/A'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_class = '';
                                        $status_icon = '';
                                        switch ($record['status']) {
                                            case 'present':
                                                $status_class = 'bg-green-100 text-green-800';
                                                $status_icon = 'fas fa-check';
                                                break;
                                            case 'absent':
                                                $status_class = 'bg-red-100 text-red-800';
                                                $status_icon = 'fas fa-times';
                                                break;
                                            case 'late':
                                                $status_class = 'bg-yellow-100 text-yellow-800';
                                                $status_icon = 'fas fa-clock';
                                                break;
                                        }
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $status_class; ?>">
                                            <i class="<?php echo $status_icon; ?> mr-1"></i>
                                            <?php echo ucfirst($record['status']); ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="mt-4 text-center">
                    <a href="my-attendance.php" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-indigo-100 hover:bg-indigo-200">
                        View All Records
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500">No attendance records found.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
