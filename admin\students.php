<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Student Management';
$success = '';
$error = '';

// Function to generate random password
function generatePassword($length = 8) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%';
    return substr(str_shuffle($chars), 0, $length);
}

// Function to generate student email
function generateStudentEmail($fullName, $domain = 'sanskar.edu.in') {
    $nameParts = explode(' ', strtolower($fullName));
    $firstName = $nameParts[0];
    $lastName = isset($nameParts[count($nameParts) - 1]) ? $nameParts[count($nameParts) - 1] : '';
    
    // Generate base email
    $baseEmail = $firstName . '.' . $lastName;
    $baseEmail = preg_replace('/[^a-z0-9.]/', '', $baseEmail);
    
    // Check for existing emails and add number if needed
    global $db;
    $email = $baseEmail . '@' . $domain;
    $counter = 1;
    
    while (true) {
        $stmt = $db->prepare("SELECT id FROM users WHERE email = :email");
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            break;
        }
        
        $email = $baseEmail . $counter . '@' . $domain;
        $counter++;
    }
    
    return $email;
}

// Handle form submissions
if ($_POST) {
    if ($_POST['action'] === 'add_student') {
        $full_name = trim($_POST['full_name']);
        $department_id = $_POST['department_id'];
        $phone = trim($_POST['phone']);
        $address = trim($_POST['address']);
        $generate_credentials = isset($_POST['generate_credentials']);
        
        if (!empty($full_name) && !empty($department_id)) {
            try {
                // Generate or use provided credentials
                if ($generate_credentials) {
                    $email = generateStudentEmail($full_name);
                    $password = generatePassword();
                    $username = strtolower(str_replace(' ', '.', $full_name)) . rand(100, 999);
                } else {
                    $email = trim($_POST['email']);
                    $username = trim($_POST['username']);
                    $password = $_POST['password'];
                }
                
                // Check if username or email already exists
                $stmt = $db->prepare("SELECT id FROM users WHERE username = :username OR email = :email");
                $stmt->bindParam(':username', $username);
                $stmt->bindParam(':email', $email);
                $stmt->execute();
                
                if ($stmt->rowCount() > 0) {
                    $error = "Username or email already exists";
                } else {
                    // Insert new student
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $db->prepare("
                        INSERT INTO users (username, email, password, full_name, role, phone, address, is_active, created_at)
                        VALUES (:username, :email, :password, :full_name, 'student', :phone, :address, 1, NOW())
                    ");
                    $stmt->bindParam(':username', $username);
                    $stmt->bindParam(':email', $email);
                    $stmt->bindParam(':password', $hashed_password);
                    $stmt->bindParam(':full_name', $full_name);
                    $stmt->bindParam(':phone', $phone);
                    $stmt->bindParam(':address', $address);
                    $stmt->execute();
                    
                    $student_id = $db->lastInsertId();
                    
                    // Store generated credentials for display
                    if ($generate_credentials) {
                        $_SESSION['generated_credentials'] = [
                            'full_name' => $full_name,
                            'username' => $username,
                            'email' => $email,
                            'password' => $password
                        ];
                    }
                    
                    $success = "Student added successfully!";
                }
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        } else {
            $error = "Please fill in all required fields";
        }
    } elseif ($_POST['action'] === 'import_students') {
        // Handle file upload
        if (isset($_FILES['import_file']) && $_FILES['import_file']['error'] == 0) {
            $uploadedFile = $_FILES['import_file']['tmp_name'];
            $fileType = pathinfo($_FILES['import_file']['name'], PATHINFO_EXTENSION);
            
            if ($fileType == 'csv') {
                $imported = 0;
                $failed = 0;
                $credentials = [];
                
                if (($handle = fopen($uploadedFile, "r")) !== FALSE) {
                    // Skip header row
                    $header = fgetcsv($handle, 1000, ",");
                    
                    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                        $full_name = trim($data[0]);
                        $department_code = trim($data[1]);
                        $course_code = isset($data[2]) ? trim($data[2]) : '';
                        $class_section = isset($data[3]) ? trim($data[3]) : '';
                        $phone = isset($data[4]) ? trim($data[4]) : '';
                        $address = isset($data[5]) ? trim($data[5]) : '';
                        
                        if (empty($full_name) || empty($department_code)) {
                            $failed++;
                            continue;
                        }
                        
                        try {
                            // Get department ID
                            $stmt = $db->prepare("SELECT id FROM departments WHERE code = :code");
                            $stmt->bindParam(':code', $department_code);
                            $stmt->execute();
                            $dept = $stmt->fetch(PDO::FETCH_ASSOC);
                            
                            if (!$dept) {
                                $failed++;
                                continue;
                            }
                            
                            // Generate credentials
                            $email = generateStudentEmail($full_name);
                            $password = generatePassword();
                            $username = strtolower(str_replace(' ', '.', $full_name)) . rand(100, 999);
                            
                            // Insert student
                            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                            $stmt = $db->prepare("
                                INSERT INTO users (username, email, password, full_name, role, phone, address, is_active, created_at)
                                VALUES (:username, :email, :password, :full_name, 'student', :phone, :address, 1, NOW())
                            ");
                            $stmt->bindParam(':username', $username);
                            $stmt->bindParam(':email', $email);
                            $stmt->bindParam(':password', $hashed_password);
                            $stmt->bindParam(':full_name', $full_name);
                            $stmt->bindParam(':phone', $phone);
                            $stmt->bindParam(':address', $address);
                            $stmt->execute();
                            
                            $student_id = $db->lastInsertId();
                            
                            // If course and class info provided, enroll student
                            if (!empty($course_code) && !empty($class_section)) {
                                $stmt = $db->prepare("
                                    SELECT c.id FROM classes c
                                    JOIN courses co ON c.course_id = co.id
                                    WHERE co.code = :course_code AND c.section = :section
                                    ORDER BY c.id DESC LIMIT 1
                                ");
                                $stmt->bindParam(':course_code', $course_code);
                                $stmt->bindParam(':section', $class_section);
                                $stmt->execute();
                                $class = $stmt->fetch(PDO::FETCH_ASSOC);
                                
                                if ($class) {
                                    $stmt = $db->prepare("
                                        INSERT INTO enrollments (student_id, class_id, enrollment_date, status)
                                        VALUES (:student_id, :class_id, NOW(), 'active')
                                    ");
                                    $stmt->bindParam(':student_id', $student_id);
                                    $stmt->bindParam(':class_id', $class['id']);
                                    $stmt->execute();
                                }
                            }
                            
                            $credentials[] = [
                                'full_name' => $full_name,
                                'username' => $username,
                                'email' => $email,
                                'password' => $password
                            ];
                            
                            $imported++;
                        } catch (PDOException $e) {
                            $failed++;
                        }
                    }
                    fclose($handle);
                    
                    $_SESSION['imported_credentials'] = $credentials;
                    $success = "Import completed! Imported: $imported students, Failed: $failed";
                }
            } else {
                $error = "Please upload a CSV file";
            }
        } else {
            $error = "Error uploading file";
        }
    } elseif ($_POST['action'] === 'enroll_student') {
        $student_id = $_POST['student_id'];
        $class_id = $_POST['class_id'];
        
        try {
            $stmt = $db->prepare("
                INSERT INTO enrollments (student_id, class_id, enrollment_date, status)
                VALUES (:student_id, :class_id, NOW(), 'active')
            ");
            $stmt->bindParam(':student_id', $student_id);
            $stmt->bindParam(':class_id', $class_id);
            $stmt->execute();
            
            $success = "Student enrolled successfully!";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                $error = "Student is already enrolled in this class";
            } else {
                $error = "Error enrolling student: " . $e->getMessage();
            }
        }
    }
}

// Get all students with department info
try {
    $stmt = $db->query("
        SELECT u.*, 
               COUNT(DISTINCT e.class_id) as enrolled_classes
        FROM users u
        LEFT JOIN enrollments e ON u.id = e.student_id AND e.status = 'active'
        WHERE u.role = 'student'
        GROUP BY u.id
        ORDER BY u.created_at DESC
    ");
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get departments for dropdown
    $stmt = $db->query("SELECT id, name, code FROM departments ORDER BY name");
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get classes for enrollment
    $stmt = $db->query("
        SELECT c.*, co.name as course_name, co.code as course_code, 
               u.full_name as teacher_name
        FROM classes c
        JOIN courses co ON c.course_id = co.id
        JOIN users u ON c.teacher_id = u.id
        ORDER BY co.name, c.section
    ");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching data: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Student Management</h1>
            <p class="mt-2 text-gray-600">Manage students, enrollments, and credentials</p>
        </div>
        <div class="space-x-3">
            <button onclick="toggleImportModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-file-import mr-2"></i>Import Students
            </button>
            <button onclick="toggleAddStudentModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-plus mr-2"></i>Add Student
            </button>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-check-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($success); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <!-- Generated Credentials Display -->
    <?php if (isset($_SESSION['generated_credentials'])): ?>
        <?php $creds = $_SESSION['generated_credentials']; unset($_SESSION['generated_credentials']); ?>
        <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-blue-900 mb-2">Generated Credentials</h3>
            <div class="bg-white rounded p-3 font-mono text-sm">
                <p><strong>Name:</strong> <?php echo htmlspecialchars($creds['full_name']); ?></p>
                <p><strong>Username:</strong> <?php echo htmlspecialchars($creds['username']); ?></p>
                <p><strong>Email:</strong> <?php echo htmlspecialchars($creds['email']); ?></p>
                <p><strong>Password:</strong> <?php echo htmlspecialchars($creds['password']); ?></p>
            </div>
            <button onclick="copyCredentials(<?php echo htmlspecialchars(json_encode($creds)); ?>)" 
                    class="mt-3 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                <i class="fas fa-copy mr-1"></i>Copy Credentials
            </button>
        </div>
    <?php endif; ?>

    <!-- Imported Credentials Display -->
    <?php if (isset($_SESSION['imported_credentials']) && count($_SESSION['imported_credentials']) > 0): ?>
        <?php $importedCreds = $_SESSION['imported_credentials']; unset($_SESSION['imported_credentials']); ?>
        <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-blue-900 mb-2">Imported Student Credentials</h3>
            <div class="max-h-64 overflow-y-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Username</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Password</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($importedCreds as $cred): ?>
                            <tr>
                                <td class="px-4 py-2 text-sm"><?php echo htmlspecialchars($cred['full_name']); ?></td>
                                <td class="px-4 py-2 text-sm font-mono"><?php echo htmlspecialchars($cred['username']); ?></td>
                                <td class="px-4 py-2 text-sm font-mono"><?php echo htmlspecialchars($cred['email']); ?></td>
                                <td class="px-4 py-2 text-sm font-mono"><?php echo htmlspecialchars($cred['password']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <button onclick="downloadCredentials(<?php echo htmlspecialchars(json_encode($importedCreds)); ?>)" 
                    class="mt-3 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                <i class="fas fa-download mr-1"></i>Download All Credentials
            </button>
        </div>
    <?php endif; ?>

    <!-- Students Table -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">All Students</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enrolled Classes</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($students as $student): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($student['full_name']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($student['email']); ?></div>
                                    <div class="text-xs text-gray-400">@<?php echo htmlspecialchars($student['username']); ?></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo htmlspecialchars($student['phone'] ?: 'N/A'); ?></div>
                                <div class="text-xs text-gray-500"><?php echo htmlspecialchars($student['address'] ?: 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    <?php echo $student['enrolled_classes']; ?> Classes
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    <?php echo $student['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                    <?php echo $student['is_active'] ? 'Active' : 'Inactive'; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showEnrollmentModal(<?php echo $student['id']; ?>, '<?php echo htmlspecialchars($student['full_name']); ?>')" 
                                        class="text-indigo-600 hover:text-indigo-900 mr-3">
                                    Enroll
                                </button>
                                <a href="student-details.php?id=<?php echo $student['id']; ?>" 
                                   class="text-blue-600 hover:text-blue-900">
                                    View Details
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Student Modal -->
<div id="addStudentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Add New Student</h3>
            </div>
            <form method="POST" class="p-6">
                <input type="hidden" name="action" value="add_student">
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                        <input type="text" name="full_name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Department *</label>
                        <select name="department_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">Select Department</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo $dept['id']; ?>"><?php echo htmlspecialchars($dept['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                        <input type="tel" name="phone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                        <textarea name="address" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                    </div>
                    
                    <div class="border-t pt-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="generate_credentials" checked onchange="toggleCredentialFields(this)" class="mr-2">
                            <span class="text-sm font-medium text-gray-700">Auto-generate email and password</span>
                        </label>
                    </div>
                    
                    <div id="manualCredentials" class="space-y-4 hidden">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                            <input type="text" name="username" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" name="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <input type="password" name="password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="toggleAddStudentModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        Add Student
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Students Modal -->
<div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Import Students</h3>
            </div>
            <form method="POST" enctype="multipart/form-data" class="p-6">
                <input type="hidden" name="action" value="import_students">
                
                <div class="mb-6">
                    <h4 class="font-medium text-gray-900 mb-2">CSV Format Requirements:</h4>
                    <div class="bg-gray-50 rounded p-4 text-sm">
                        <p class="mb-2">The CSV file should have the following columns:</p>
                        <ol class="list-decimal list-inside space-y-1 text-gray-700">
                            <li><strong>Full Name</strong> (Required)</li>
                            <li><strong>Department Code</strong> (Required - e.g., CS, IT, MS, EC)</li>
                            <li><strong>Course Code</strong> (Optional - for auto-enrollment)</li>
                            <li><strong>Class Section</strong> (Optional - for auto-enrollment)</li>
                            <li><strong>Phone</strong> (Optional)</li>
                            <li><strong>Address</strong> (Optional)</li>
                        </ol>
                        <p class="mt-3 text-gray-600">Email and password will be automatically generated for each student.</p>
                    </div>
                </div>
                
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Select CSV File</label>
                    <input type="file" name="import_file" accept=".csv" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <div class="mb-4">
                    <a href="#" onclick="downloadSampleCSV()" class="text-indigo-600 hover:text-indigo-800 text-sm">
                        <i class="fas fa-download mr-1"></i>Download Sample CSV
                    </a>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="toggleImportModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                        Import Students
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Enrollment Modal -->
<div id="enrollmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Enroll Student in Class</h3>
                <p class="text-sm text-gray-600 mt-1" id="enrollStudentName"></p>
            </div>
            <form method="POST" class="p-6">
                <input type="hidden" name="action" value="enroll_student">
                <input type="hidden" name="student_id" id="enrollStudentId">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Select Class</label>
                    <select name="class_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Choose a class</option>
                        <?php foreach ($classes as $class): ?>
                            <option value="<?php echo $class['id']; ?>">
                                <?php echo htmlspecialchars($class['course_name'] . ' - ' . $class['course_code'] . ' (Section ' . $class['section'] . ')'); ?>
                                - <?php echo htmlspecialchars($class['teacher_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideEnrollmentModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        Enroll Student
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleAddStudentModal() {
    const modal = document.getElementById('addStudentModal');
    modal.classList.toggle('hidden');
}

function toggleImportModal() {
    const modal = document.getElementById('importModal');
    modal.classList.toggle('hidden');
}

function toggleCredentialFields(checkbox) {
    const manualFields = document.getElementById('manualCredentials');
    if (checkbox.checked) {
        manualFields.classList.add('hidden');
        // Clear manual fields
        manualFields.querySelectorAll('input').forEach(input => {
            input.value = '';
            input.removeAttribute('required');
        });
    } else {
        manualFields.classList.remove('hidden');
        // Make manual fields required
        manualFields.querySelectorAll('input').forEach(input => {
            input.setAttribute('required', 'required');
        });
    }
}

function showEnrollmentModal(studentId, studentName) {
    document.getElementById('enrollStudentId').value = studentId;
    document.getElementById('enrollStudentName').textContent = 'Student: ' + studentName;
    document.getElementById('enrollmentModal').classList.remove('hidden');
}

function hideEnrollmentModal() {
    document.getElementById('enrollmentModal').classList.add('hidden');
}

function copyCredentials(credentials) {
    const text = `Name: ${credentials.full_name}\nUsername: ${credentials.username}\nEmail: ${credentials.email}\nPassword: ${credentials.password}`;
    navigator.clipboard.writeText(text).then(() => {
        alert('Credentials copied to clipboard!');
    });
}

function downloadCredentials(credentials) {
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "Full Name,Username,Email,Password\n";
    
    credentials.forEach(cred => {
        csvContent += `"${cred.full_name}","${cred.username}","${cred.email}","${cred.password}"\n`;
    });
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "student_credentials_" + new Date().getTime() + ".csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function downloadSampleCSV() {
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "Full Name,Department Code,Course Code,Class Section,Phone,Address\n";
    csvContent += "John Doe,CS,CS301,A,9876543210,123 Main Street\n";
    csvContent += "Jane Smith,IT,IT302,B,9876543211,456 Oak Avenue\n";
    csvContent += "Robert Johnson,MS,MS201,A,9876543212,789 Pine Road\n";
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "sample_students_import.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Auto-hide success messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert-auto-hide');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.transition = 'opacity 0.5s';
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 500);
        }, 5000);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
