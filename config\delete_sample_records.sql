-- <PERSON><PERSON>t to delete all sample records from the Attendance Management System

-- Disable foreign key checks to allow deletion
SET FOREIGN_KEY_CHECKS = 0;

-- Delete sample attendance records
DELETE FROM attendance_records 
WHERE student_id IN (SELECT id FROM users WHERE role IN ('student', 'teacher'))
OR marked_by IN (SELECT id FROM users WHERE role IN ('student', 'teacher'));

-- Delete sample attendance sessions
DELETE FROM attendance_sessions 
WHERE created_by IN (SELECT id FROM users WHERE role IN ('student', 'teacher'));

-- Delete sample enrollments
DELETE FROM enrollments 
WHERE student_id IN (SELECT id FROM users WHERE role IN ('student', 'teacher'));

-- Delete sample classes
DELETE FROM classes 
WHERE teacher_id IN (SELECT id FROM users WHERE role IN ('student', 'teacher'));

-- Reset department heads to NULL
UPDATE departments SET head_id = NULL;

-- Delete sample courses
DELETE FROM courses 
WHERE department_id IN (SELECT id FROM departments);

-- Delete sample users (students and teachers)
DELETE FROM users 
WHERE role IN ('student', 'teacher');

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Verify deletions
SELECT 'Sample records deleted successfully!' as message;
SELECT 'Remaining Users:' as info, COUNT(*) as count FROM users;
SELECT 'Remaining Departments:' as info, COUNT(*) as count FROM departments;
SELECT 'Remaining Courses:' as info, COUNT(*) as count FROM courses;
SELECT 'Remaining Classes:' as info, COUNT(*) as count FROM classes;
SELECT 'Remaining Enrollments:' as info, COUNT(*) as count FROM enrollments;
SELECT 'Remaining Attendance Sessions:' as info, COUNT(*) as count FROM attendance_sessions;
SELECT 'Remaining Attendance Records:' as info, COUNT(*) as count FROM attendance_records;
