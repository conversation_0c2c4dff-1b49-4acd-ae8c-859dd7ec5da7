<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Class Management';
$success = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if ($_POST['action'] === 'add_class') {
        $course_id = $_POST['course_id'];
        $teacher_id = $_POST['teacher_id'];
        $section = trim($_POST['section']);
        $semester = $_POST['semester'];
        $academic_year = $_POST['academic_year'];
        $schedule = trim($_POST['schedule']);
        $room = trim($_POST['room']);
        
        if (!empty($course_id) && !empty($teacher_id) && !empty($section)) {
            try {
                // Check if class already exists
                $stmt = $db->prepare("
                    SELECT id FROM classes 
                    WHERE course_id = :course_id AND section = :section 
                    AND semester = :semester AND academic_year = :academic_year
                ");
                $stmt->bindParam(':course_id', $course_id);
                $stmt->bindParam(':section', $section);
                $stmt->bindParam(':semester', $semester);
                $stmt->bindParam(':academic_year', $academic_year);
                $stmt->execute();
                
                if ($stmt->rowCount() > 0) {
                    $error = "Class with this course and section already exists for this semester";
                } else {
                    // Insert new class
                    $stmt = $db->prepare("
                        INSERT INTO classes (course_id, teacher_id, section, semester, academic_year, schedule, room, created_at)
                        VALUES (:course_id, :teacher_id, :section, :semester, :academic_year, :schedule, :room, NOW())
                    ");
                    $stmt->bindParam(':course_id', $course_id);
                    $stmt->bindParam(':teacher_id', $teacher_id);
                    $stmt->bindParam(':section', $section);
                    $stmt->bindParam(':semester', $semester);
                    $stmt->bindParam(':academic_year', $academic_year);
                    $stmt->bindParam(':schedule', $schedule);
                    $stmt->bindParam(':room', $room);
                    $stmt->execute();
                    
                    $success = "Class added successfully!";
                }
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        } else {
            $error = "Please fill in all required fields";
        }
    } elseif ($_POST['action'] === 'edit_class') {
        $id = $_POST['class_id'];
        $course_id = $_POST['course_id'];
        $teacher_id = $_POST['teacher_id'];
        $section = trim($_POST['section']);
        $semester = $_POST['semester'];
        $academic_year = $_POST['academic_year'];
        $schedule = trim($_POST['schedule']);
        $room = trim($_POST['room']);
        
        if (!empty($course_id) && !empty($teacher_id) && !empty($section)) {
            try {
                // Check if class already exists (excluding current class)
                $stmt = $db->prepare("
                    SELECT id FROM classes 
                    WHERE course_id = :course_id AND section = :section 
                    AND semester = :semester AND academic_year = :academic_year
                    AND id != :id
                ");
                $stmt->bindParam(':course_id', $course_id);
                $stmt->bindParam(':section', $section);
                $stmt->bindParam(':semester', $semester);
                $stmt->bindParam(':academic_year', $academic_year);
                $stmt->bindParam(':id', $id);
                $stmt->execute();
                
                if ($stmt->rowCount() > 0) {
                    $error = "Class with this course and section already exists for this semester";
                } else {
                    // Update class
                    $stmt = $db->prepare("
                        UPDATE classes 
                        SET course_id = :course_id, teacher_id = :teacher_id, section = :section,
                            semester = :semester, academic_year = :academic_year, 
                            schedule = :schedule, room = :room
                        WHERE id = :id
                    ");
                    $stmt->bindParam(':course_id', $course_id);
                    $stmt->bindParam(':teacher_id', $teacher_id);
                    $stmt->bindParam(':section', $section);
                    $stmt->bindParam(':semester', $semester);
                    $stmt->bindParam(':academic_year', $academic_year);
                    $stmt->bindParam(':schedule', $schedule);
                    $stmt->bindParam(':room', $room);
                    $stmt->bindParam(':id', $id);
                    $stmt->execute();
                    
                    $success = "Class updated successfully!";
                }
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        } else {
            $error = "Please fill in all required fields";
        }
    } elseif ($_POST['action'] === 'delete_class') {
        $id = $_POST['class_id'];
        
        try {
            // Check if class has enrollments
            $stmt = $db->prepare("SELECT COUNT(*) FROM enrollments WHERE class_id = :id");
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            $enrollment_count = $stmt->fetchColumn();
            
            if ($enrollment_count > 0) {
                $error = "Cannot delete class. It has $enrollment_count enrolled students.";
            } else {
                // Delete class
                $stmt = $db->prepare("DELETE FROM classes WHERE id = :id");
                $stmt->bindParam(':id', $id);
                $stmt->execute();
                
                $success = "Class deleted successfully!";
            }
        } catch (PDOException $e) {
            $error = "Error deleting class: " . $e->getMessage();
        }
    }
}

// Get courses for dropdown
try {
    $stmt = $db->query("
        SELECT c.id, c.name, c.code, d.name as department_name
        FROM courses c
        JOIN departments d ON c.department_id = d.id
        ORDER BY d.name, c.name
    ");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching courses: " . $e->getMessage();
}

// Get teachers for dropdown
try {
    $stmt = $db->query("
        SELECT id, full_name, email
        FROM users 
        WHERE role = 'teacher' AND is_active = 1
        ORDER BY full_name
    ");
    $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching teachers: " . $e->getMessage();
}

// Get classes with related info
try {
    $stmt = $db->query("
        SELECT cl.*, c.name as course_name, c.code as course_code,
               d.name as department_name, u.full_name as teacher_name,
               COUNT(e.id) as enrollment_count
        FROM classes cl
        JOIN courses c ON cl.course_id = c.id
        JOIN departments d ON c.department_id = d.id
        JOIN users u ON cl.teacher_id = u.id
        LEFT JOIN enrollments e ON cl.id = e.class_id AND e.status = 'active'
        GROUP BY cl.id
        ORDER BY cl.academic_year DESC, cl.semester DESC, d.name, c.name, cl.section
    ");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching classes: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Class Management</h1>
            <p class="mt-2 text-gray-600">Manage class sections and assignments</p>
        </div>
        <button onclick="toggleAddModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
            <i class="fas fa-plus mr-2"></i>Add Class
        </button>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md alert-auto-hide">
            <div class="flex">
                <i class="fas fa-check-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($success); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <!-- Classes Table -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">All Classes</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($classes as $class): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($class['course_name']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($class['course_code']); ?></div>
                                    <div class="text-xs text-gray-400"><?php echo htmlspecialchars($class['department_name']); ?></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($class['section']); ?></div>
                                <div class="text-xs text-gray-500"><?php echo htmlspecialchars($class['semester']); ?> <?php echo htmlspecialchars($class['academic_year']); ?></div>
                                <?php if ($class['room']): ?>
                                    <div class="text-xs text-gray-400">Room: <?php echo htmlspecialchars($class['room']); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($class['teacher_name']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo $class['schedule'] ? htmlspecialchars($class['schedule']) : 'Not set'; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    <?php echo $class['enrollment_count']; ?> Student<?php echo $class['enrollment_count'] != 1 ? 's' : ''; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="editClass(<?php echo $class['id']; ?>, <?php echo $class['course_id']; ?>, <?php echo $class['teacher_id']; ?>, '<?php echo htmlspecialchars($class['section'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($class['semester'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($class['academic_year'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($class['schedule'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($class['room'], ENT_QUOTES); ?>')" 
                                        class="text-indigo-600 hover:text-indigo-900 mr-3">
                                    Edit
                                </button>
                                <?php if ($class['enrollment_count'] == 0): ?>
                                    <form method="POST" class="inline">
                                        <input type="hidden" name="action" value="delete_class">
                                        <input type="hidden" name="class_id" value="<?php echo $class['id']; ?>">
                                        <button type="submit" class="text-red-600 hover:text-red-900" 
                                                onclick="return confirmAction('Are you sure you want to delete this class?')">
                                            Delete
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Class Modal -->
<div id="classModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">Add Class</h3>
            </div>
            <form method="POST" class="p-6">
                <input type="hidden" id="modalAction" name="action" value="add_class">
                <input type="hidden" id="classId" name="class_id" value="">
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Course</label>
                        <select id="classCourse" name="course_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">Select Course</option>
                            <?php foreach ($courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>">
                                    <?php echo htmlspecialchars($course['department_name'] . ' - ' . $course['name'] . ' (' . $course['code'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Teacher</label>
                        <select id="classTeacher" name="teacher_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">Select Teacher</option>
                            <?php foreach ($teachers as $teacher): ?>
                                <option value="<?php echo $teacher['id']; ?>">
                                    <?php echo htmlspecialchars($teacher['full_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Section</label>
                            <input type="text" id="classSection" name="section" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="A, B, C...">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Semester</label>
                            <select id="classSemester" name="semester" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">Select Semester</option>
                                <option value="Fall">Fall</option>
                                <option value="Spring">Spring</option>
                                <option value="Summer">Summer</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Academic Year</label>
                            <input type="text" id="classYear" name="academic_year" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="2024-2025" value="2024-2025">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Room</label>
                            <input type="text" id="classRoom" name="room" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="Room 101">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Schedule</label>
                        <input type="text" id="classSchedule" name="schedule" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="Mon/Wed/Fri 10:00-11:00 AM">
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="toggleAddModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" id="submitBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        Add Class
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleAddModal() {
    const modal = document.getElementById('classModal');
    modal.classList.toggle('hidden');
    
    // Reset form for add mode
    document.getElementById('modalTitle').textContent = 'Add Class';
    document.getElementById('modalAction').value = 'add_class';
    document.getElementById('submitBtn').textContent = 'Add Class';
    document.getElementById('classId').value = '';
    document.getElementById('classCourse').value = '';
    document.getElementById('classTeacher').value = '';
    document.getElementById('classSection').value = '';
    document.getElementById('classSemester').value = '';
    document.getElementById('classYear').value = '2024-2025';
    document.getElementById('classRoom').value = '';
    document.getElementById('classSchedule').value = '';
}

function editClass(id, courseId, teacherId, section, semester, year, schedule, room) {
    const modal = document.getElementById('classModal');
    modal.classList.remove('hidden');
    
    // Set form for edit mode
    document.getElementById('modalTitle').textContent = 'Edit Class';
    document.getElementById('modalAction').value = 'edit_class';
    document.getElementById('submitBtn').textContent = 'Update Class';
    document.getElementById('classId').value = id;
    document.getElementById('classCourse').value = courseId;
    document.getElementById('classTeacher').value = teacherId;
    document.getElementById('classSection').value = section;
    document.getElementById('classSemester').value = semester;
    document.getElementById('classYear').value = year;
    document.getElementById('classRoom').value = room;
    document.getElementById('classSchedule').value = schedule;
}
</script>

<?php include '../includes/footer.php'; ?>
