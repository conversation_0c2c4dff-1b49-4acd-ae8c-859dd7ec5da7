<?php
session_start();
require_once '../../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../../auth/login.php');
    exit();
}

$page_title = 'Teacher Reports';
$error = '';

// Get filter parameters
$department_filter = $_GET['department_id'] ?? '';

// Get departments for filter
try {
    $stmt = $db->query("SELECT id, name FROM departments ORDER BY name");
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching departments: " . $e->getMessage();
}

// Build query for teacher data
$where_conditions = ["u.role = 'teacher'", "u.is_active = 1"];
$params = [];

if ($department_filter) {
    $where_conditions[] = "d.id = :department_id";
    $params[':department_id'] = $department_filter;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get teacher data with class and session statistics
try {
    $stmt = $db->prepare("
        SELECT 
            u.id,
            u.full_name,
            u.username,
            u.email,
            u.phone,
            COUNT(DISTINCT cl.id) as total_classes,
            COUNT(DISTINCT ats.id) as total_sessions,
            COUNT(DISTINCT c.department_id) as departments_count,
            GROUP_CONCAT(DISTINCT d.name SEPARATOR ', ') as departments,
            MAX(ats.date) as last_session_date,
            AVG(
                CASE 
                    WHEN session_stats.total_students > 0 THEN 
                        (session_stats.present_count + session_stats.late_count) / session_stats.total_students * 100
                    ELSE 0 
                END
            ) as avg_attendance_rate
        FROM users u
        LEFT JOIN classes cl ON u.id = cl.teacher_id
        LEFT JOIN courses c ON cl.course_id = c.id
        LEFT JOIN departments d ON c.department_id = d.id
        LEFT JOIN attendance_sessions ats ON cl.id = ats.class_id
        LEFT JOIN (
            SELECT 
                ats.id as session_id,
                COUNT(e.id) as total_students,
                SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) as present_count,
                SUM(CASE WHEN ar.status = 'late' THEN 1 ELSE 0 END) as late_count
            FROM attendance_sessions ats
            JOIN classes cl ON ats.class_id = cl.id
            LEFT JOIN enrollments e ON cl.id = e.class_id AND e.status = 'active'
            LEFT JOIN attendance_records ar ON ats.id = ar.session_id AND ar.student_id = e.student_id
            GROUP BY ats.id
        ) session_stats ON ats.id = session_stats.session_id
        $where_clause
        GROUP BY u.id
        ORDER BY u.full_name
    ");
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    
    $stmt->execute();
    $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error = "Error fetching teacher data: " . $e->getMessage();
}

include '../../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Teacher Reports</h1>
        <p class="mt-2 text-gray-600">View teacher workload and session statistics</p>
    </div>

    <!-- Filters -->
    <div class="mb-6 bg-white p-6 rounded-lg shadow">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                <select name="department_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="">All Departments</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" <?php echo $department_filter == $dept['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($dept['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="flex items-end">
                <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
            </div>
        </form>
    </div>

    <!-- Summary Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <?php
        $total_teachers = count($teachers);
        $total_classes = array_sum(array_column($teachers, 'total_classes'));
        $total_sessions = array_sum(array_column($teachers, 'total_sessions'));
        $avg_attendance = $total_teachers > 0 ? round(array_sum(array_column($teachers, 'avg_attendance_rate')) / $total_teachers, 1) : 0;
        ?>
        
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-chalkboard-teacher text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Teachers</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_teachers; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-chalkboard text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Classes</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_classes; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-calendar-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Sessions</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_sessions; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-percentage text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Avg Attendance</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $avg_attendance; ?>%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Message -->
    <?php if ($error): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <!-- Teachers Table -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">Teacher List</h3>
            <?php if (!empty($teachers)): ?>
                <button onclick="exportToCSV()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm">
                    <i class="fas fa-download mr-2"></i>Export CSV
                </button>
            <?php endif; ?>
        </div>
        
        <?php if (!empty($teachers)): ?>
            <div class="overflow-x-auto">
                <table id="teachersTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Departments</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Classes</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Attendance</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Session</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($teachers as $teacher): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($teacher['full_name']); ?></div>
                                    <div class="text-sm text-gray-500">@<?php echo htmlspecialchars($teacher['username']); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?php echo htmlspecialchars($teacher['email']); ?></div>
                                    <?php if ($teacher['phone']): ?>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($teacher['phone']); ?></div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <?php echo $teacher['departments'] ? htmlspecialchars($teacher['departments']) : 'None'; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                        <?php echo $teacher['total_classes']; ?> Class<?php echo $teacher['total_classes'] != 1 ? 'es' : ''; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        <?php echo $teacher['total_sessions']; ?> Session<?php echo $teacher['total_sessions'] != 1 ? 's' : ''; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="text-sm font-medium text-gray-900 mr-2">
                                            <?php echo $teacher['avg_attendance_rate'] ? round($teacher['avg_attendance_rate'], 1) : 0; ?>%
                                        </div>
                                        <div class="w-16 bg-gray-200 rounded-full h-2">
                                            <div class="h-2 rounded-full <?php 
                                                $rate = $teacher['avg_attendance_rate'] ?: 0;
                                                echo $rate >= 85 ? 'bg-green-500' : 
                                                    ($rate >= 75 ? 'bg-yellow-500' : 'bg-red-500'); 
                                            ?>" style="width: <?php echo min(100, $rate); ?>%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $teacher['last_session_date'] ? date('M d, Y', strtotime($teacher['last_session_date'])) : 'Never'; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <i class="fas fa-chalkboard-teacher text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">No teachers found for the selected criteria.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function exportToCSV() {
    const table = document.getElementById('teachersTable');
    const rows = table.querySelectorAll('tr');
    let csv = [];
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cols = row.querySelectorAll('td, th');
        let csvRow = [];
        
        for (let j = 0; j < cols.length; j++) {
            csvRow.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
        }
        
        csv.push(csvRow.join(','));
    }
    
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'teacher_report_' + new Date().toISOString().split('T')[0] + '.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>

<?php include '../../includes/footer.php'; ?>
