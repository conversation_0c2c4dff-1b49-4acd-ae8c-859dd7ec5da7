-- Sample data for Sanskar Institute Attendance Management System
-- Run this after importing schema.sql to populate the database with demo data

USE attendance_system;

-- Insert sample teachers
INSERT INTO users (username, email, password, full_name, role, phone, address) VALUES 
('teacher1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Dr. <PERSON><PERSON>', 'teacher', '9876543211', 'Faculty Quarters, Sanskar Institute'),
('teacher2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Prof<PERSON>', 'teacher', '9876543212', 'Faculty Quarters, Sanskar Institute'),
('teacher3', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Dr. <PERSON>', 'teacher', '9876543213', 'Faculty Quarters, Sanskar Institute'),
('teacher4', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ms. Neha Gupta', 'teacher', '9876543214', 'Faculty Quarters, Sanskar Institute');

-- Insert sample students
INSERT INTO users (username, email, password, full_name, role, phone, address) VALUES 
('student001', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Arjun Singh', 'student', '9876543301', 'Hostel Block A, Room 101'),
('student002', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Priya Verma', 'student', '9876543302', 'Hostel Block A, Room 102'),
('student003', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Rohit Sharma', 'student', '9876543303', 'Hostel Block A, Room 103'),
('student004', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sneha Patel', 'student', '9876543304', 'Hostel Block A, Room 104'),
('student005', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Vikram Joshi', 'student', '9876543305', 'Hostel Block A, Room 105'),
('student006', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Anita Reddy', 'student', '9876543306', 'Hostel Block B, Room 201'),
('student007', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Karan Mehta', 'student', '9876543307', 'Hostel Block B, Room 202'),
('student008', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Pooja Agarwal', 'student', '9876543308', 'Hostel Block B, Room 203'),
('student009', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Rahul Yadav', 'student', '9876543309', 'Hostel Block B, Room 204'),
('student010', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Kavya Nair', 'student', '9876543310', 'Hostel Block B, Room 205'),
('student011', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Deepak Kumar', 'student', '9876543311', 'Hostel Block C, Room 301'),
('student012', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Riya Shah', 'student', '9876543312', 'Hostel Block C, Room 302'),
('student013', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Manish Tiwari', 'student', '9876543313', 'Hostel Block C, Room 303'),
('student014', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Swati Jain', 'student', '9876543314', 'Hostel Block C, Room 304'),
('student015', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Aditya Singh', 'student', '9876543315', 'Hostel Block C, Room 305');

-- Update department heads
UPDATE departments SET head_id = (SELECT id FROM users WHERE username = 'teacher1') WHERE code = 'CS';
UPDATE departments SET head_id = (SELECT id FROM users WHERE username = 'teacher2') WHERE code = 'IT';
UPDATE departments SET head_id = (SELECT id FROM users WHERE username = 'teacher3') WHERE code = 'MS';
UPDATE departments SET head_id = (SELECT id FROM users WHERE username = 'teacher4') WHERE code = 'EC';

-- Insert more courses
INSERT INTO courses (name, code, department_id, semester, credits) VALUES 
('Object Oriented Programming', 'CS302', 1, 3, 4),
('Computer Networks', 'CS401', 1, 4, 3),
('Software Engineering', 'CS402', 1, 4, 4),
('Web Development', 'IT301', 2, 3, 3),
('System Administration', 'IT401', 2, 4, 3),
('Mobile App Development', 'IT402', 2, 4, 4),
('Financial Management', 'MS301', 3, 3, 3),
('Marketing Management', 'MS302', 3, 3, 3),
('Human Resource Management', 'MS401', 3, 4, 3),
('Microprocessors', 'EC201', 4, 2, 4),
('Communication Systems', 'EC301', 4, 3, 4),
('VLSI Design', 'EC401', 4, 4, 4);

-- Insert classes with teacher assignments
INSERT INTO classes (course_id, teacher_id, section, academic_year) VALUES 
-- CS Department Classes
(1, (SELECT id FROM users WHERE username = 'teacher1'), 'A', '2024-25'),
(1, (SELECT id FROM users WHERE username = 'teacher1'), 'B', '2024-25'),
(5, (SELECT id FROM users WHERE username = 'teacher1'), 'A', '2024-25'),
(6, (SELECT id FROM users WHERE username = 'teacher1'), 'A', '2024-25'),
(7, (SELECT id FROM users WHERE username = 'teacher1'), 'A', '2024-25'),

-- IT Department Classes
(2, (SELECT id FROM users WHERE username = 'teacher2'), 'A', '2024-25'),
(2, (SELECT id FROM users WHERE username = 'teacher2'), 'B', '2024-25'),
(8, (SELECT id FROM users WHERE username = 'teacher2'), 'A', '2024-25'),
(9, (SELECT id FROM users WHERE username = 'teacher2'), 'A', '2024-25'),
(10, (SELECT id FROM users WHERE username = 'teacher2'), 'A', '2024-25'),

-- MS Department Classes
(3, (SELECT id FROM users WHERE username = 'teacher3'), 'A', '2024-25'),
(11, (SELECT id FROM users WHERE username = 'teacher3'), 'A', '2024-25'),
(12, (SELECT id FROM users WHERE username = 'teacher3'), 'A', '2024-25'),
(13, (SELECT id FROM users WHERE username = 'teacher3'), 'A', '2024-25'),

-- EC Department Classes
(4, (SELECT id FROM users WHERE username = 'teacher4'), 'A', '2024-25'),
(14, (SELECT id FROM users WHERE username = 'teacher4'), 'A', '2024-25'),
(15, (SELECT id FROM users WHERE username = 'teacher4'), 'A', '2024-25'),
(16, (SELECT id FROM users WHERE username = 'teacher4'), 'A', '2024-25');

-- Enroll students in classes
-- CS Students (student001-005) in CS classes
INSERT INTO enrollments (student_id, class_id, status) VALUES 
((SELECT id FROM users WHERE username = 'student001'), 1, 'active'),
((SELECT id FROM users WHERE username = 'student001'), 3, 'active'),
((SELECT id FROM users WHERE username = 'student001'), 4, 'active'),
((SELECT id FROM users WHERE username = 'student002'), 1, 'active'),
((SELECT id FROM users WHERE username = 'student002'), 3, 'active'),
((SELECT id FROM users WHERE username = 'student002'), 4, 'active'),
((SELECT id FROM users WHERE username = 'student003'), 2, 'active'),
((SELECT id FROM users WHERE username = 'student003'), 5, 'active'),
((SELECT id FROM users WHERE username = 'student004'), 2, 'active'),
((SELECT id FROM users WHERE username = 'student004'), 5, 'active'),
((SELECT id FROM users WHERE username = 'student005'), 1, 'active'),
((SELECT id FROM users WHERE username = 'student005'), 3, 'active');

-- IT Students (student006-010) in IT classes
INSERT INTO enrollments (student_id, class_id, status) VALUES 
((SELECT id FROM users WHERE username = 'student006'), 6, 'active'),
((SELECT id FROM users WHERE username = 'student006'), 8, 'active'),
((SELECT id FROM users WHERE username = 'student006'), 9, 'active'),
((SELECT id FROM users WHERE username = 'student007'), 6, 'active'),
((SELECT id FROM users WHERE username = 'student007'), 8, 'active'),
((SELECT id FROM users WHERE username = 'student007'), 10, 'active'),
((SELECT id FROM users WHERE username = 'student008'), 7, 'active'),
((SELECT id FROM users WHERE username = 'student008'), 9, 'active'),
((SELECT id FROM users WHERE username = 'student009'), 7, 'active'),
((SELECT id FROM users WHERE username = 'student009'), 10, 'active'),
((SELECT id FROM users WHERE username = 'student010'), 6, 'active'),
((SELECT id FROM users WHERE username = 'student010'), 8, 'active');

-- MS Students (student011-013) in MS classes
INSERT INTO enrollments (student_id, class_id, status) VALUES 
((SELECT id FROM users WHERE username = 'student011'), 11, 'active'),
((SELECT id FROM users WHERE username = 'student011'), 12, 'active'),
((SELECT id FROM users WHERE username = 'student011'), 13, 'active'),
((SELECT id FROM users WHERE username = 'student012'), 11, 'active'),
((SELECT id FROM users WHERE username = 'student012'), 12, 'active'),
((SELECT id FROM users WHERE username = 'student012'), 14, 'active'),
((SELECT id FROM users WHERE username = 'student013'), 11, 'active'),
((SELECT id FROM users WHERE username = 'student013'), 13, 'active'),
((SELECT id FROM users WHERE username = 'student013'), 14, 'active');

-- EC Students (student014-015) in EC classes
INSERT INTO enrollments (student_id, class_id, status) VALUES 
((SELECT id FROM users WHERE username = 'student014'), 15, 'active'),
((SELECT id FROM users WHERE username = 'student014'), 16, 'active'),
((SELECT id FROM users WHERE username = 'student014'), 17, 'active'),
((SELECT id FROM users WHERE username = 'student015'), 15, 'active'),
((SELECT id FROM users WHERE username = 'student015'), 16, 'active'),
((SELECT id FROM users WHERE username = 'student015'), 18, 'active');

-- Insert sample attendance sessions (last 2 weeks)
INSERT INTO attendance_sessions (class_id, date, start_time, end_time, topic, created_by) VALUES 
-- CS Classes
(1, '2024-05-20', '09:00:00', '10:30:00', 'Arrays and Linked Lists', (SELECT id FROM users WHERE username = 'teacher1')),
(1, '2024-05-22', '09:00:00', '10:30:00', 'Stacks and Queues', (SELECT id FROM users WHERE username = 'teacher1')),
(1, '2024-05-24', '09:00:00', '10:30:00', 'Trees and Graphs', (SELECT id FROM users WHERE username = 'teacher1')),
(2, '2024-05-21', '11:00:00', '12:30:00', 'Binary Search Trees', (SELECT id FROM users WHERE username = 'teacher1')),
(2, '2024-05-23', '11:00:00', '12:30:00', 'Hash Tables', (SELECT id FROM users WHERE username = 'teacher1')),

-- IT Classes
(6, '2024-05-20', '14:00:00', '15:30:00', 'SQL Basics', (SELECT id FROM users WHERE username = 'teacher2')),
(6, '2024-05-22', '14:00:00', '15:30:00', 'Joins and Subqueries', (SELECT id FROM users WHERE username = 'teacher2')),
(6, '2024-05-24', '14:00:00', '15:30:00', 'Database Design', (SELECT id FROM users WHERE username = 'teacher2')),
(7, '2024-05-21', '16:00:00', '17:30:00', 'Normalization', (SELECT id FROM users WHERE username = 'teacher2')),

-- MS Classes
(11, '2024-05-20', '10:00:00', '11:30:00', 'Data Visualization', (SELECT id FROM users WHERE username = 'teacher3')),
(11, '2024-05-22', '10:00:00', '11:30:00', 'Statistical Analysis', (SELECT id FROM users WHERE username = 'teacher3')),
(11, '2024-05-24', '10:00:00', '11:30:00', 'Predictive Modeling', (SELECT id FROM users WHERE username = 'teacher3')),

-- EC Classes
(15, '2024-05-20', '13:00:00', '14:30:00', 'Logic Gates', (SELECT id FROM users WHERE username = 'teacher4')),
(15, '2024-05-22', '13:00:00', '14:30:00', 'Boolean Algebra', (SELECT id FROM users WHERE username = 'teacher4')),
(15, '2024-05-24', '13:00:00', '14:30:00', 'Combinational Circuits', (SELECT id FROM users WHERE username = 'teacher4'));

-- Insert sample attendance records
-- Session 1 (CS - Data Structures - Section A)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(1, (SELECT id FROM users WHERE username = 'student001'), 'present', (SELECT id FROM users WHERE username = 'teacher1')),
(1, (SELECT id FROM users WHERE username = 'student002'), 'present', (SELECT id FROM users WHERE username = 'teacher1')),
(1, (SELECT id FROM users WHERE username = 'student005'), 'late', (SELECT id FROM users WHERE username = 'teacher1'));

-- Session 2 (CS - Data Structures - Section A)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(2, (SELECT id FROM users WHERE username = 'student001'), 'present', (SELECT id FROM users WHERE username = 'teacher1')),
(2, (SELECT id FROM users WHERE username = 'student002'), 'absent', (SELECT id FROM users WHERE username = 'teacher1')),
(2, (SELECT id FROM users WHERE username = 'student005'), 'present', (SELECT id FROM users WHERE username = 'teacher1'));

-- Session 3 (CS - Data Structures - Section A)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(3, (SELECT id FROM users WHERE username = 'student001'), 'present', (SELECT id FROM users WHERE username = 'teacher1')),
(3, (SELECT id FROM users WHERE username = 'student002'), 'present', (SELECT id FROM users WHERE username = 'teacher1')),
(3, (SELECT id FROM users WHERE username = 'student005'), 'present', (SELECT id FROM users WHERE username = 'teacher1'));

-- Session 4 (CS - Data Structures - Section B)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(4, (SELECT id FROM users WHERE username = 'student003'), 'present', (SELECT id FROM users WHERE username = 'teacher1')),
(4, (SELECT id FROM users WHERE username = 'student004'), 'present', (SELECT id FROM users WHERE username = 'teacher1'));

-- Session 5 (CS - Data Structures - Section B)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(5, (SELECT id FROM users WHERE username = 'student003'), 'absent', (SELECT id FROM users WHERE username = 'teacher1')),
(5, (SELECT id FROM users WHERE username = 'student004'), 'present', (SELECT id FROM users WHERE username = 'teacher1'));

-- Session 6 (IT - Database Management - Section A)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(6, (SELECT id FROM users WHERE username = 'student006'), 'present', (SELECT id FROM users WHERE username = 'teacher2')),
(6, (SELECT id FROM users WHERE username = 'student007'), 'present', (SELECT id FROM users WHERE username = 'teacher2')),
(6, (SELECT id FROM users WHERE username = 'student010'), 'late', (SELECT id FROM users WHERE username = 'teacher2'));

-- Session 7 (IT - Database Management - Section A)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(7, (SELECT id FROM users WHERE username = 'student006'), 'present', (SELECT id FROM users WHERE username = 'teacher2')),
(7, (SELECT id FROM users WHERE username = 'student007'), 'absent', (SELECT id FROM users WHERE username = 'teacher2')),
(7, (SELECT id FROM users WHERE username = 'student010'), 'present', (SELECT id FROM users WHERE username = 'teacher2'));

-- Session 8 (IT - Database Management - Section A)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(8, (SELECT id FROM users WHERE username = 'student006'), 'present', (SELECT id FROM users WHERE username = 'teacher2')),
(8, (SELECT id FROM users WHERE username = 'student007'), 'present', (SELECT id FROM users WHERE username = 'teacher2')),
(8, (SELECT id FROM users WHERE username = 'student010'), 'present', (SELECT id FROM users WHERE username = 'teacher2'));

-- Session 9 (IT - Database Management - Section B)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(9, (SELECT id FROM users WHERE username = 'student008'), 'present', (SELECT id FROM users WHERE username = 'teacher2')),
(9, (SELECT id FROM users WHERE username = 'student009'), 'present', (SELECT id FROM users WHERE username = 'teacher2'));

-- Session 10 (MS - Business Analytics)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(10, (SELECT id FROM users WHERE username = 'student011'), 'present', (SELECT id FROM users WHERE username = 'teacher3')),
(10, (SELECT id FROM users WHERE username = 'student012'), 'present', (SELECT id FROM users WHERE username = 'teacher3')),
(10, (SELECT id FROM users WHERE username = 'student013'), 'absent', (SELECT id FROM users WHERE username = 'teacher3'));

-- Session 11 (MS - Business Analytics)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(11, (SELECT id FROM users WHERE username = 'student011'), 'present', (SELECT id FROM users WHERE username = 'teacher3')),
(11, (SELECT id FROM users WHERE username = 'student012'), 'late', (SELECT id FROM users WHERE username = 'teacher3')),
(11, (SELECT id FROM users WHERE username = 'student013'), 'present', (SELECT id FROM users WHERE username = 'teacher3'));

-- Session 12 (MS - Business Analytics)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(12, (SELECT id FROM users WHERE username = 'student011'), 'present', (SELECT id FROM users WHERE username = 'teacher3')),
(12, (SELECT id FROM users WHERE username = 'student012'), 'present', (SELECT id FROM users WHERE username = 'teacher3')),
(12, (SELECT id FROM users WHERE username = 'student013'), 'present', (SELECT id FROM users WHERE username = 'teacher3'));

-- Session 13 (EC - Digital Electronics)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(13, (SELECT id FROM users WHERE username = 'student014'), 'present', (SELECT id FROM users WHERE username = 'teacher4')),
(13, (SELECT id FROM users WHERE username = 'student015'), 'present', (SELECT id FROM users WHERE username = 'teacher4'));

-- Session 14 (EC - Digital Electronics)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(14, (SELECT id FROM users WHERE username = 'student014'), 'present', (SELECT id FROM users WHERE username = 'teacher4')),
(14, (SELECT id FROM users WHERE username = 'student015'), 'absent', (SELECT id FROM users WHERE username = 'teacher4'));

-- Session 15 (EC - Digital Electronics)
INSERT INTO attendance_records (session_id, student_id, status, marked_by) VALUES 
(15, (SELECT id FROM users WHERE username = 'student014'), 'late', (SELECT id FROM users WHERE username = 'teacher4')),
(15, (SELECT id FROM users WHERE username = 'student015'), 'present', (SELECT id FROM users WHERE username = 'teacher4'));

-- Display summary
SELECT 'Sample data inserted successfully!' as message;
SELECT 'Total Users:' as info, COUNT(*) as count FROM users;
SELECT 'Total Departments:' as info, COUNT(*) as count FROM departments;
SELECT 'Total Courses:' as info, COUNT(*) as count FROM courses;
SELECT 'Total Classes:' as info, COUNT(*) as count FROM classes;
SELECT 'Total Enrollments:' as info, COUNT(*) as count FROM enrollments;
SELECT 'Total Attendance Sessions:' as info, COUNT(*) as count FROM attendance_sessions;
SELECT 'Total Attendance Records:' as info, COUNT(*) as count FROM attendance_records;
