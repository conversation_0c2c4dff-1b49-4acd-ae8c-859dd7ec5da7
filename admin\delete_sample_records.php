<?php
require_once '../config/database.php';

try {
    // Start a database connection
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // Read the delete sample records SQL script
    $sqlScript = file_get_contents('../config/delete_sample_records.sql');

    // Execute the script
    if ($conn->multi_query($sqlScript)) {
        // Process all results
        do {
            if ($result = $conn->store_result()) {
                $result->free();
            }
        } while ($conn->next_result());

        echo json_encode([
            'status' => 'success', 
            'message' => 'Sample records deleted successfully!'
        ]);
    } else {
        throw new Exception("Error executing delete script: " . $conn->error);
    }

    // Close the connection
    $conn->close();

} catch (Exception $e) {
    // Handle any errors
    echo json_encode([
        'status' => 'error', 
        'message' => $e->getMessage()
    ]);
}
