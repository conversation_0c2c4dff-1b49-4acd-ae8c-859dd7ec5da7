<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit();
}

// Get student ID from URL
$student_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$student_id) {
    header('Location: students.php');
    exit();
}

$page_title = 'Student Details';
$success = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if ($_POST['action'] === 'update_student') {
        $full_name = trim($_POST['full_name']);
        $email = trim($_POST['email']);
        $phone = trim($_POST['phone']);
        $address = trim($_POST['address']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        try {
            $stmt = $db->prepare("
                UPDATE users 
                SET full_name = :full_name, email = :email, phone = :phone, 
                    address = :address, is_active = :is_active
                WHERE id = :id AND role = 'student'
            ");
            $stmt->bindParam(':full_name', $full_name);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':phone', $phone);
            $stmt->bindParam(':address', $address);
            $stmt->bindParam(':is_active', $is_active);
            $stmt->bindParam(':id', $student_id);
            $stmt->execute();
            
            $success = "Student information updated successfully!";
        } catch (PDOException $e) {
            $error = "Error updating student: " . $e->getMessage();
        }
    } elseif ($_POST['action'] === 'reset_password') {
        $new_password = $_POST['new_password'];
        
        try {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $db->prepare("UPDATE users SET password = :password WHERE id = :id");
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':id', $student_id);
            $stmt->execute();
            
            $success = "Password reset successfully!";
        } catch (PDOException $e) {
            $error = "Error resetting password: " . $e->getMessage();
        }
    } elseif ($_POST['action'] === 'unenroll') {
        $enrollment_id = $_POST['enrollment_id'];
        
        try {
            $stmt = $db->prepare("UPDATE enrollments SET status = 'dropped' WHERE id = :id");
            $stmt->bindParam(':id', $enrollment_id);
            $stmt->execute();
            
            $success = "Student unenrolled from class successfully!";
        } catch (PDOException $e) {
            $error = "Error unenrolling student: " . $e->getMessage();
        }
    }
}

// Get student details
try {
    $stmt = $db->prepare("
        SELECT * FROM users 
        WHERE id = :id AND role = 'student'
    ");
    $stmt->bindParam(':id', $student_id);
    $stmt->execute();
    $student = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$student) {
        header('Location: students.php');
        exit();
    }
    
    // Get student enrollments
    $stmt = $db->prepare("
        SELECT e.*, c.section, c.semester, c.academic_year, c.schedule, c.room,
               co.name as course_name, co.code as course_code, co.credits,
               u.full_name as teacher_name,
               d.name as department_name
        FROM enrollments e
        JOIN classes c ON e.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        JOIN users u ON c.teacher_id = u.id
        JOIN departments d ON co.department_id = d.id
        WHERE e.student_id = :student_id
        ORDER BY e.status DESC, e.enrollment_date DESC
    ");
    $stmt->bindParam(':student_id', $student_id);
    $stmt->execute();
    $enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get attendance summary
    $stmt = $db->prepare("
        SELECT 
            COUNT(DISTINCT ar.session_id) as total_sessions,
            SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) as present_count,
            SUM(CASE WHEN ar.status = 'absent' THEN 1 ELSE 0 END) as absent_count,
            SUM(CASE WHEN ar.status = 'late' THEN 1 ELSE 0 END) as late_count
        FROM attendance_records ar
        JOIN attendance_sessions ats ON ar.session_id = ats.id
        WHERE ar.student_id = :student_id
    ");
    $stmt->bindParam(':student_id', $student_id);
    $stmt->execute();
    $attendance_summary = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $attendance_percentage = 0;
    if ($attendance_summary['total_sessions'] > 0) {
        $attendance_percentage = round(
            (($attendance_summary['present_count'] + $attendance_summary['late_count']) / 
            $attendance_summary['total_sessions']) * 100, 2
        );
    }
    
} catch (PDOException $e) {
    $error = "Error fetching student details: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Back Button -->
    <div class="mb-6">
        <a href="students.php" class="text-indigo-600 hover:text-indigo-800">
            <i class="fas fa-arrow-left mr-2"></i>Back to Students
        </a>
    </div>

    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900"><?php echo htmlspecialchars($student['full_name']); ?></h1>
        <p class="mt-2 text-gray-600">Student ID: <?php echo $student['id']; ?></p>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-check-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($success); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Student Information -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow-lg rounded-lg overflow-hidden mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Student Information</h3>
                </div>
                <form method="POST" class="p-6">
                    <input type="hidden" name="action" value="update_student">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                            <input type="text" name="full_name" value="<?php echo htmlspecialchars($student['full_name']); ?>" 
                                   required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                            <input type="text" value="<?php echo htmlspecialchars($student['username']); ?>" 
                                   disabled class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" name="email" value="<?php echo htmlspecialchars($student['email']); ?>" 
                                   required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                            <input type="tel" name="phone" value="<?php echo htmlspecialchars($student['phone'] ?: ''); ?>" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                            <textarea name="address" rows="2" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"><?php echo htmlspecialchars($student['address'] ?: ''); ?></textarea>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="is_active" <?php echo $student['is_active'] ? 'checked' : ''; ?> class="mr-2">
                                <span class="text-sm font-medium text-gray-700">Active Status</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md">
                            Update Information
                        </button>
                    </div>
                </form>
            </div>

            <!-- Enrollments -->
            <div class="bg-white shadow-lg rounded-lg overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Class Enrollments</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php if (count($enrollments) > 0): ?>
                                <?php foreach ($enrollments as $enrollment): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($enrollment['course_name']); ?>
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    <?php echo htmlspecialchars($enrollment['course_code']); ?> - 
                                                    <?php echo $enrollment['credits']; ?> Credits
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">Section <?php echo htmlspecialchars($enrollment['section']); ?></div>
                                            <div class="text-xs text-gray-500">
                                                <?php echo htmlspecialchars($enrollment['semester'] . ' ' . $enrollment['academic_year']); ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo htmlspecialchars($enrollment['teacher_name']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                <?php echo $enrollment['status'] === 'active' ? 'bg-green-100 text-green-800' : 
                                                    ($enrollment['status'] === 'completed' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'); ?>">
                                                <?php echo ucfirst($enrollment['status']); ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <?php if ($enrollment['status'] === 'active'): ?>
                                                <form method="POST" class="inline">
                                                    <input type="hidden" name="action" value="unenroll">
                                                    <input type="hidden" name="enrollment_id" value="<?php echo $enrollment['id']; ?>">
                                                    <button type="submit" class="text-red-600 hover:text-red-900"
                                                            onclick="return confirm('Are you sure you want to unenroll this student from this class?')">
                                                        Unenroll
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                        No enrollments found
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Attendance Summary -->
            <div class="bg-white shadow-lg rounded-lg overflow-hidden mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Attendance Summary</h3>
                </div>
                <div class="p-6">
                    <div class="text-center mb-4">
                        <div class="text-4xl font-bold text-indigo-600"><?php echo $attendance_percentage; ?>%</div>
                        <div class="text-sm text-gray-600">Overall Attendance</div>
                    </div>
                    
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Total Sessions:</span>
                            <span class="text-sm font-medium"><?php echo $attendance_summary['total_sessions']; ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Present:</span>
                            <span class="text-sm font-medium text-green-600"><?php echo $attendance_summary['present_count']; ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Absent:</span>
                            <span class="text-sm font-medium text-red-600"><?php echo $attendance_summary['absent_count']; ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Late:</span>
                            <span class="text-sm font-medium text-yellow-600"><?php echo $attendance_summary['late_count']; ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Actions -->
            <div class="bg-white shadow-lg rounded-lg overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Account Actions</h3>
                </div>
                <div class="p-6">
                    <button onclick="togglePasswordModal()" class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md mb-3">
                        <i class="fas fa-key mr-2"></i>Reset Password
                    </button>
                    
                    <div class="text-sm text-gray-600 mt-4">
                        <p><strong>Created:</strong> <?php echo date('M d, Y', strtotime($student['created_at'])); ?></p>
                        <p><strong>Last Updated:</strong> <?php echo date('M d, Y', strtotime($student['updated_at'])); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Password Reset Modal -->
<div id="passwordModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Reset Password</h3>
            </div>
            <form method="POST" class="p-6">
                <input type="hidden" name="action" value="reset_password">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                    <input type="password" name="new_password" required minlength="6"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <p class="text-xs text-gray-500 mt-1">Minimum 6 characters</p>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="togglePasswordModal()" 
                            class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                        Reset Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function togglePasswordModal() {
    const modal = document.getElementById('passwordModal');
    modal.classList.toggle('hidden');
}
</script>

<?php include '../includes/footer.php'; ?>
