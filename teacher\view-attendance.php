<?php
session_start();
require_once '../config/database.php';

// Check if user is teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'View Attendance';
$teacher_id = $_SESSION['user_id'];
$error = '';

// Get filter parameters
$class_filter = $_GET['class_id'] ?? '';
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // Today

// Get teacher's classes
try {
    $stmt = $db->prepare("
        SELECT cl.id, c.name as course_name, c.code as course_code, 
               cl.section, cl.semester, cl.academic_year
        FROM classes cl
        JOIN courses c ON cl.course_id = c.id
        WHERE cl.teacher_id = :teacher_id
        ORDER BY c.name, cl.section
    ");
    $stmt->bindParam(':teacher_id', $teacher_id);
    $stmt->execute();
    $teacher_classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching classes: " . $e->getMessage();
}

// Build query for attendance sessions
$where_conditions = ["cl.teacher_id = :teacher_id"];
$params = [':teacher_id' => $teacher_id];

if ($class_filter) {
    $where_conditions[] = "cl.id = :class_id";
    $params[':class_id'] = $class_filter;
}

if ($date_from) {
    $where_conditions[] = "ats.date >= :date_from";
    $params[':date_from'] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "ats.date <= :date_to";
    $params[':date_to'] = $date_to;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get attendance sessions with statistics
try {
    $stmt = $db->prepare("
        SELECT 
            ats.id,
            ats.date,
            ats.start_time,
            ats.end_time,
            ats.topic,
            c.name as course_name,
            c.code as course_code,
            cl.section,
            COUNT(e.id) as total_students,
            COUNT(ar.id) as marked_students,
            SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) as present_count,
            SUM(CASE WHEN ar.status = 'absent' THEN 1 ELSE 0 END) as absent_count,
            SUM(CASE WHEN ar.status = 'late' THEN 1 ELSE 0 END) as late_count
        FROM attendance_sessions ats
        JOIN classes cl ON ats.class_id = cl.id
        JOIN courses c ON cl.course_id = c.id
        LEFT JOIN enrollments e ON cl.id = e.class_id AND e.status = 'active'
        LEFT JOIN attendance_records ar ON ats.id = ar.session_id
        $where_clause
        GROUP BY ats.id
        ORDER BY ats.date DESC, ats.start_time DESC
    ");
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    
    $stmt->execute();
    $attendance_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching attendance sessions: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">View Attendance</h1>
        <p class="mt-2 text-gray-600">Review attendance sessions and records</p>
    </div>

    <!-- Filters -->
    <div class="mb-6 bg-white p-6 rounded-lg shadow">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Class</label>
                <select name="class_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="">All Classes</option>
                    <?php foreach ($teacher_classes as $class): ?>
                        <option value="<?php echo $class['id']; ?>" <?php echo $class_filter == $class['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($class['course_name'] . ' - ' . $class['section']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                <input type="date" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                <input type="date" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            
            <div class="flex items-end">
                <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
            </div>
        </form>
    </div>

    <!-- Error Message -->
    <?php if ($error): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <!-- Attendance Sessions -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Attendance Sessions</h3>
        </div>
        
        <?php if (!empty($attendance_sessions)): ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($attendance_sessions as $session): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        <?php echo date('M d, Y', strtotime($session['date'])); ?>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <?php echo date('h:i A', strtotime($session['start_time'])) . ' - ' . date('h:i A', strtotime($session['end_time'])); ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($session['course_name']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($session['course_code'] . ' - ' . $session['section']); ?></div>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <?php echo $session['topic'] ? htmlspecialchars($session['topic']) : '-'; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><?php echo $session['marked_students']; ?></span> / <?php echo $session['total_students']; ?> students
                                    </div>
                                    <div class="flex space-x-2 mt-1">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            P: <?php echo $session['present_count']; ?>
                                        </span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                            A: <?php echo $session['absent_count']; ?>
                                        </span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            L: <?php echo $session['late_count']; ?>
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="mark-attendance.php?session_id=<?php echo $session['id']; ?>" 
                                       class="text-indigo-600 hover:text-indigo-900 mr-3">
                                        <?php echo $session['marked_students'] > 0 ? 'Edit' : 'Mark'; ?>
                                    </a>
                                    <button onclick="viewDetails(<?php echo $session['id']; ?>)" 
                                            class="text-blue-600 hover:text-blue-900">
                                        Details
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">No attendance sessions found for the selected criteria.</p>
                <a href="mark-attendance.php" class="mt-4 inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                    <i class="fas fa-plus mr-2"></i>Create New Session
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Session Details Modal -->
<div id="detailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Session Details</h3>
            </div>
            <div id="modalContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
                <button onclick="closeModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function viewDetails(sessionId) {
    const modal = document.getElementById('detailsModal');
    const content = document.getElementById('modalContent');
    
    // Show loading
    content.innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-gray-400 text-2xl"></i><p class="text-gray-500 mt-2">Loading...</p></div>';
    modal.classList.remove('hidden');
    
    // Fetch session details
    fetch(`get-session-details.php?session_id=${sessionId}`)
        .then(response => response.text())
        .then(data => {
            content.innerHTML = data;
        })
        .catch(error => {
            content.innerHTML = '<div class="text-center py-8"><i class="fas fa-exclamation-triangle text-red-400 text-2xl"></i><p class="text-red-500 mt-2">Error loading details</p></div>';
        });
}

function closeModal() {
    document.getElementById('detailsModal').classList.add('hidden');
}
</script>

<?php include '../includes/footer.php'; ?>
