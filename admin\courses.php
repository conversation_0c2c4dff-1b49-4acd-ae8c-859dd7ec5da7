<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Course Management';
$success = '';
$error = '';

// Get filter parameters
$department_filter = $_GET['department_id'] ?? '';

// Handle form submissions
if ($_POST) {
    if ($_POST['action'] === 'add_course') {
        $name = trim($_POST['name']);
        $code = trim($_POST['code']);
        $department_id = $_POST['department_id'];
        $credits = $_POST['credits'];
        $description = trim($_POST['description']);
        
        if (!empty($name) && !empty($code) && !empty($department_id)) {
            try {
                // Check if code already exists
                $stmt = $db->prepare("SELECT id FROM courses WHERE code = :code");
                $stmt->bindParam(':code', $code);
                $stmt->execute();
                
                if ($stmt->rowCount() > 0) {
                    $error = "Course code already exists";
                } else {
                    // Insert new course
                    $stmt = $db->prepare("
                        INSERT INTO courses (name, code, department_id, credits, description, created_at)
                        VALUES (:name, :code, :department_id, :credits, :description, NOW())
                    ");
                    $stmt->bindParam(':name', $name);
                    $stmt->bindParam(':code', $code);
                    $stmt->bindParam(':department_id', $department_id);
                    $stmt->bindParam(':credits', $credits);
                    $stmt->bindParam(':description', $description);
                    $stmt->execute();
                    
                    $success = "Course added successfully!";
                }
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        } else {
            $error = "Please fill in all required fields";
        }
    } elseif ($_POST['action'] === 'edit_course') {
        $id = $_POST['course_id'];
        $name = trim($_POST['name']);
        $code = trim($_POST['code']);
        $department_id = $_POST['department_id'];
        $credits = $_POST['credits'];
        $description = trim($_POST['description']);
        
        if (!empty($name) && !empty($code) && !empty($department_id)) {
            try {
                // Check if code already exists for other courses
                $stmt = $db->prepare("SELECT id FROM courses WHERE code = :code AND id != :id");
                $stmt->bindParam(':code', $code);
                $stmt->bindParam(':id', $id);
                $stmt->execute();
                
                if ($stmt->rowCount() > 0) {
                    $error = "Course code already exists";
                } else {
                    // Update course
                    $stmt = $db->prepare("
                        UPDATE courses 
                        SET name = :name, code = :code, department_id = :department_id, 
                            credits = :credits, description = :description
                        WHERE id = :id
                    ");
                    $stmt->bindParam(':name', $name);
                    $stmt->bindParam(':code', $code);
                    $stmt->bindParam(':department_id', $department_id);
                    $stmt->bindParam(':credits', $credits);
                    $stmt->bindParam(':description', $description);
                    $stmt->bindParam(':id', $id);
                    $stmt->execute();
                    
                    $success = "Course updated successfully!";
                }
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        } else {
            $error = "Please fill in all required fields";
        }
    } elseif ($_POST['action'] === 'delete_course') {
        $id = $_POST['course_id'];
        
        try {
            // Check if course has classes
            $stmt = $db->prepare("SELECT COUNT(*) FROM classes WHERE course_id = :id");
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            $class_count = $stmt->fetchColumn();
            
            if ($class_count > 0) {
                $error = "Cannot delete course. It has $class_count associated classes.";
            } else {
                // Delete course
                $stmt = $db->prepare("DELETE FROM courses WHERE id = :id");
                $stmt->bindParam(':id', $id);
                $stmt->execute();
                
                $success = "Course deleted successfully!";
            }
        } catch (PDOException $e) {
            $error = "Error deleting course: " . $e->getMessage();
        }
    }
}

// Get all departments for dropdown
try {
    $stmt = $db->query("SELECT id, name FROM departments ORDER BY name");
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching departments: " . $e->getMessage();
}

// Get courses with department info and class count
try {
    $where_clause = '';
    $params = [];
    
    if ($department_filter) {
        $where_clause = 'WHERE c.department_id = :department_id';
        $params[':department_id'] = $department_filter;
    }
    
    $stmt = $db->prepare("
        SELECT c.*, d.name as department_name, COUNT(cl.id) as class_count
        FROM courses c
        JOIN departments d ON c.department_id = d.id
        LEFT JOIN classes cl ON c.id = cl.course_id
        $where_clause
        GROUP BY c.id
        ORDER BY d.name, c.name
    ");
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    
    $stmt->execute();
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching courses: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Course Management</h1>
            <p class="mt-2 text-gray-600">Manage academic courses</p>
        </div>
        <button onclick="toggleAddModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
            <i class="fas fa-plus mr-2"></i>Add Course
        </button>
    </div>

    <!-- Filters -->
    <div class="mb-6 bg-white p-4 rounded-lg shadow">
        <form method="GET" class="flex items-center space-x-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Filter by Department</label>
                <select name="department_id" onchange="this.form.submit()" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <option value="">All Departments</option>
                    <?php foreach ($departments as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" <?php echo $department_filter == $dept['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($dept['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </form>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md alert-auto-hide">
            <div class="flex">
                <i class="fas fa-check-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($success); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <!-- Courses Table -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">All Courses</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credits</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Classes</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($courses as $course): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($course['name']); ?></div>
                                    <div class="text-sm text-gray-500 font-mono"><?php echo htmlspecialchars($course['code']); ?></div>
                                    <?php if ($course['description']): ?>
                                        <div class="text-xs text-gray-400 mt-1"><?php echo htmlspecialchars(substr($course['description'], 0, 100)); ?><?php echo strlen($course['description']) > 100 ? '...' : ''; ?></div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($course['department_name']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo $course['credits']; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    <?php echo $course['class_count']; ?> Class<?php echo $course['class_count'] != 1 ? 'es' : ''; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="editCourse(<?php echo $course['id']; ?>, '<?php echo htmlspecialchars($course['name'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($course['code'], ENT_QUOTES); ?>', <?php echo $course['department_id']; ?>, <?php echo $course['credits']; ?>, '<?php echo htmlspecialchars($course['description'], ENT_QUOTES); ?>')" 
                                        class="text-indigo-600 hover:text-indigo-900 mr-3">
                                    Edit
                                </button>
                                <?php if ($course['class_count'] == 0): ?>
                                    <form method="POST" class="inline">
                                        <input type="hidden" name="action" value="delete_course">
                                        <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">
                                        <button type="submit" class="text-red-600 hover:text-red-900" 
                                                onclick="return confirmAction('Are you sure you want to delete this course?')">
                                            Delete
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Course Modal -->
<div id="courseModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">Add Course</h3>
            </div>
            <form method="POST" class="p-6">
                <input type="hidden" id="modalAction" name="action" value="add_course">
                <input type="hidden" id="courseId" name="course_id" value="">
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Course Name</label>
                        <input type="text" id="courseName" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Course Code</label>
                        <input type="text" id="courseCode" name="code" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                        <select id="courseDepartment" name="department_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">Select Department</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo $dept['id']; ?>"><?php echo htmlspecialchars($dept['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Credits</label>
                        <input type="number" id="courseCredits" name="credits" min="1" max="10" value="3" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea id="courseDescription" name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="toggleAddModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" id="submitBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        Add Course
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleAddModal() {
    const modal = document.getElementById('courseModal');
    modal.classList.toggle('hidden');
    
    // Reset form for add mode
    document.getElementById('modalTitle').textContent = 'Add Course';
    document.getElementById('modalAction').value = 'add_course';
    document.getElementById('submitBtn').textContent = 'Add Course';
    document.getElementById('courseId').value = '';
    document.getElementById('courseName').value = '';
    document.getElementById('courseCode').value = '';
    document.getElementById('courseDepartment').value = '';
    document.getElementById('courseCredits').value = '3';
    document.getElementById('courseDescription').value = '';
}

function editCourse(id, name, code, departmentId, credits, description) {
    const modal = document.getElementById('courseModal');
    modal.classList.remove('hidden');
    
    // Set form for edit mode
    document.getElementById('modalTitle').textContent = 'Edit Course';
    document.getElementById('modalAction').value = 'edit_course';
    document.getElementById('submitBtn').textContent = 'Update Course';
    document.getElementById('courseId').value = id;
    document.getElementById('courseName').value = name;
    document.getElementById('courseCode').value = code;
    document.getElementById('courseDepartment').value = departmentId;
    document.getElementById('courseCredits').value = credits;
    document.getElementById('courseDescription').value = description;
}
</script>

<?php include '../includes/footer.php'; ?>
