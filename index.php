<?php
session_start();
require_once 'config/database.php';

// Redirect to login if not authenticated
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit();
}

// Redirect based on user role
if ($_SESSION['role'] === 'admin') {
    header('Location: admin/dashboard.php');
} elseif ($_SESSION['role'] === 'teacher') {
    header('Location: teacher/dashboard.php');
} elseif ($_SESSION['role'] === 'student') {
    header('Location: student/dashboard.php');
}
exit();
?>
